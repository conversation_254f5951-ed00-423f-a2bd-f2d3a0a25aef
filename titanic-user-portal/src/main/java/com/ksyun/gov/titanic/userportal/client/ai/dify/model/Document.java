package com.ksyun.gov.titanic.userportal.client.ai.dify.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @Author: MYy
 * @Date: 2024/8/5
 **/
@NoArgsConstructor
@AllArgsConstructor // 显式添加全参构造
@Data
@Builder(toBuilder = true)
public class Document {
    @JsonProperty("id")
    private String id;
    @JsonProperty("position")
    private Integer position;
    @JsonProperty("data_source_type")
    private String dataSourceType;
    @JsonProperty("data_source_info")
    private DataSourceInfoDTO dataSourceInfo;
    @JsonProperty("data_source_detail_dict")
    private DataSourceDetailDictDTO dataSourceDetailDict;
    @JsonProperty("dataset_process_rule_id")
    private String datasetProcessRuleId;
    @JsonProperty("name")
    private String name;
    @JsonProperty("created_from")
    private String createdFrom;
    @JsonProperty("created_by")
    private String createdBy;
    @JsonProperty("created_at")
    private Integer createdAt;
    @JsonProperty("tokens")
    private Integer tokens;
    @JsonProperty("indexing_status")
    private String indexingStatus;
    @JsonProperty("error")
    private Object error;
    @JsonProperty("enabled")
    private Boolean enabled;
    @JsonProperty("disabled_at")
    private Object disabledAt;
    @JsonProperty("disabled_by")
    private Object disabledBy;
    @JsonProperty("archived")
    private Boolean archived;
    @JsonProperty("display_status")
    private String displayStatus;
    @JsonProperty("word_count")
    private Integer wordCount;
    @JsonProperty("hit_count")
    private Integer hitCount;
    @JsonProperty("doc_form")
    private String docForm;
    @JsonProperty("doc_metadata")
    private List<DocMetadataDTO> docMetadata;

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor // 显式添加无参构造函数
    @AllArgsConstructor // 显式添加全参构造函数
    public static class DataSourceInfoDTO {
        @JsonProperty("upload_file_id")
        private String uploadFileId;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor // 显式添加无参构造函数
    @AllArgsConstructor // 显式添加全参构造函数
    public static class DataSourceDetailDictDTO {
        @JsonProperty("upload_file")
        private UploadFileDTO uploadFile;

        @Data
        @NoArgsConstructor // 显式添加无参构造函数
        @AllArgsConstructor // 显式添加全参构造函数
        public static class UploadFileDTO {
            @JsonProperty("id")
            private String idX;
            @JsonProperty("name")
            private String name;
            @JsonProperty("size")
            private Integer size;
            @JsonProperty("extension")
            private String extension;
            @JsonProperty("mime_type")
            private String mimeType;
            @JsonProperty("created_by")
            private String createdBy;
            @JsonProperty("created_at")
            private Double createdAt;
        }
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor // 显式添加无参构造函数
    @AllArgsConstructor // 显式添加全参构造函数
    public static class DocMetadataDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("name")
        private String name;
        @JsonProperty("type")
        private String type;
        @JsonProperty("value")
        private String value;
    }
}
