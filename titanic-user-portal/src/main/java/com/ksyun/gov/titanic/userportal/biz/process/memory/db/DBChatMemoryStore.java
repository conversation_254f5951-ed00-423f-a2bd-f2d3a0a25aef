package com.ksyun.gov.titanic.userportal.biz.process.memory.db;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ksyun.gov.fd.common.json.JsonUtil;
import com.ksyun.gov.fd.common.lang.Safes;
import com.ksyun.gov.fd.common.lang.Yssert;
import com.ksyun.gov.titanic.userportal.biz.process.memory.ChatDel;
import com.ksyun.gov.titanic.userportal.biz.process.memory.ChatMemoryStore;
import com.ksyun.gov.titanic.userportal.biz.process.memory.ChatMessage;
import com.ksyun.gov.titanic.userportal.biz.process.memory.ChatRecord;
import com.ksyun.gov.titanic.userportal.biz.process.memory.*;
import com.ksyun.gov.titanic.userportal.common.enums.KbCateEnum;
import com.ksyun.gov.titanic.userportal.model.entity.AiChatEntity;
import com.ksyun.gov.titanic.userportal.model.entity.ChatRecordEntity;
import com.ksyun.gov.titanic.userportal.model.entity.ChatRecordExtendInfo;
import com.ksyun.gov.titanic.userportal.model.entity.KnowledgeBaseFileEntity;
import com.ksyun.gov.titanic.userportal.model.param.RefsParam;
import com.ksyun.gov.titanic.userportal.model.vo.AiChatDetailVo;
import com.ksyun.gov.titanic.userportal.model.vo.ItemVo;
import com.ksyun.gov.titanic.userportal.model.vo.OutputMsg;
import com.ksyun.gov.titanic.userportal.service.AiChatService;
import com.ksyun.gov.titanic.userportal.service.ChatRecordService;
import com.ksyun.gov.titanic.userportal.service.KnowledgeBaseFileService;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: MYy
 * @Date: 2024/5/8
 **/
@Component
public class DBChatMemoryStore implements ChatMemoryStore {

    @Autowired
    private ChatRecordService chatRecordService;

    @Autowired
    private AiChatService aiChatService;

    @Autowired
    private KnowledgeBaseFileService knowledgeBaseFileService;


    @Override
    public String saveOrUpdateMsg (ChatMessage chatMessage) {
        ChatRecordEntity chatRecord = convertToEntity(chatMessage);
        chatRecordService.saveOrUpdate(chatRecord);
        return chatRecord.getId();
    }

    @Override
    public Boolean saveMsgs (List<ChatMessage> chatMessages) {
        return chatRecordService.saveOrUpdateBatch(Safes.of(chatMessages).stream().map(this::convertToEntity).collect(Collectors.toList()));
    }


    /**
     * 根据groupCode获取 此轮对话中的问题Id
     *
     * @param groupCode
     * @return
     */
    @Override
    public String msgId (String groupCode) {
        Yssert.notEmpty(groupCode, "您传入编码为空，是无效的轮次编码。");
        return chatRecordService.getOne(new LambdaQueryWrapper<ChatRecordEntity>().eq(ChatRecordEntity::getGroupCode, groupCode).eq(ChatRecordEntity::getRole, "user")).getId();
    }


    private ChatRecordEntity convertToEntity (ChatMessage chatMessage) {
        ChatRecordEntity chatRecordEntity = new ChatRecordEntity();
        BeanUtils.copyProperties(chatMessage, chatRecordEntity);
        return chatRecordEntity;
    }


    @Override
    public Boolean delMsgs (ChatDel req) {
        return chatRecordService.remove(new LambdaQueryWrapper<ChatRecordEntity>().in(CollUtil.isNotEmpty(req.getGroupCodes()), ChatRecordEntity::getChatCode, req.getGroupCodes()).or().in(CollUtil.isNotEmpty(req.getMsgIds()), ChatRecordEntity::getId, req.getMsgIds()));
    }


    @Override
    public Boolean updateRecord (String msgId, String modelData, String msg) {
        ChatRecordEntity chatRecord = new ChatRecordEntity();
        chatRecord.setId(msgId);
        chatRecord.setMsg(StringUtils.isBlank(msg) ? null : msg);
        chatRecord.setModelData(StringUtils.isBlank(modelData) ? null : modelData);
        return chatRecordService.updateById(chatRecord);
    }


    @Override
    @Transactional(isolation = Isolation.READ_UNCOMMITTED)
    public AiChatDetailVo getMsgs (ChatRecord chatRecord) {
        AiChatDetailVo vo = new AiChatDetailVo();
        // 当前用户
        String userId = chatRecord.getUserId();
        String botCode = chatRecord.getBotCode();
        String chatCode = chatRecord.getChatCode();

        List<String> chatCodes = null;
        if (StringUtils.isNotBlank(botCode)) {
            chatCodes = Safes.of(aiChatService.list(new LambdaQueryWrapper<AiChatEntity>().eq(AiChatEntity::getAssistantCode, botCode)
                            .eq(StringUtils.isNotBlank(userId), AiChatEntity::getCreatedBy, userId)
                            .select(AiChatEntity::getChatCode)))
                    .stream().filter(x -> Objects.nonNull(x.getChatCode())).map(AiChatEntity::getChatCode).collect(Collectors.toList());

            if (CollUtil.isEmpty(chatCodes)) {
                vo.setTotal(0);
                vo.setItems(Arrays.asList());
                return vo;
            }
        } else if (StringUtils.isNotBlank(chatCode)) {
            chatCodes = Arrays.asList(chatCode);
        }

        LambdaQueryWrapper<ChatRecordEntity> qw = new LambdaQueryWrapper<ChatRecordEntity>()
                .in(CollUtil.isNotEmpty(chatCodes), ChatRecordEntity::getChatCode, chatCodes)
                .eq(StringUtils.isNotBlank(userId), ChatRecordEntity::getCreatedBy, userId)
                .eq(ChatRecordEntity::getStatus, 1)
                .select(ChatRecordEntity::getGroupCode);

        long count = chatRecordService.count(qw);
        vo.setTotal((int) count);
        if (count <= 0) {
            vo.setItems(Arrays.asList());
            return vo;
        }

        // groupCode去重
        qw.groupBy(ChatRecordEntity::getGroupCode);

        int limit = 20;
        String index = chatRecord.getIndex();
        boolean after = Objects.nonNull(chatRecord.getAfter());
        if (after) {
            limit = chatRecord.getAfter() > 0 ? chatRecord.getAfter() : limit;
            qw.gt(StringUtils.isNotBlank(index), ChatRecordEntity::getGroupCode, index).orderByAsc(ChatRecordEntity::getChatCode).orderByAsc(ChatRecordEntity::getCreatedTime).last("limit " + limit);
        } else {
            limit = Objects.isNull(chatRecord.getBefore()) || chatRecord.getBefore() <= 0 ? limit : chatRecord.getBefore();
            qw.lt(StringUtils.isNotBlank(index), ChatRecordEntity::getGroupCode, index).orderByDesc(ChatRecordEntity::getChatCode).orderByDesc(ChatRecordEntity::getCreatedTime).last("limit " + limit);
        }

        List<String> groupCodes = Safes.of(chatRecordService.list(qw)).stream().map(x -> x.getGroupCode()).distinct().collect(Collectors.toList());
        // 空校验
        if (CollUtil.isEmpty(groupCodes)) {
            vo.setItems(Arrays.asList());
            return vo;
        }

        // 存储会话记录涉及到的参考文件code
        Set<String> fileCodes = new HashSet<>();

        // 非空则获取轮次对应消息
        qw.clear();
        qw.in(ChatRecordEntity::getGroupCode, groupCodes)
                .in(CollUtil.isNotEmpty(chatCodes), ChatRecordEntity::getChatCode, chatCodes)
                .orderByAsc(ChatRecordEntity::getChatCode)
                .orderByAsc(ChatRecordEntity::getGroupCode)
                .orderByAsc(ChatRecordEntity::getCreatedTime)
                .last(",role = \'user\' DESC");
        List<ChatRecordEntity> chatRecords = chatRecordService.list(qw);
        List<ItemVo> itemVos = Safes.of(chatRecords).stream().map(x -> {
            ItemVo itemVo = itemVo(x);
            fileCodes.addAll(Safes.of(itemVo.getRefs()).stream().filter(a -> Objects.nonNull(a.getFileCode())).map(RefsParam::getFileCode).toList());
            return itemVo;
        }).collect(Collectors.toList());

        // 存储参考文件状态【参考文件code：状态(是否删除)】
        if (CollUtil.isNotEmpty(fileCodes)) {
            Map<String, KnowledgeBaseFileEntity> map = Safes.of(knowledgeBaseFileService.list((new LambdaQueryWrapper<KnowledgeBaseFileEntity>().in(KnowledgeBaseFileEntity::getFileCode, fileCodes.stream().toList())))).stream().collect(Collectors.toMap(KnowledgeBaseFileEntity::getFileCode, Function.identity(), (x, y) -> y));
            Safes.of(itemVos).forEach(x -> {
                Safes.of(x.getRefs()).forEach(r -> {
                    KnowledgeBaseFileEntity entity = map.get(r.getFileCode());
                    if (Objects.nonNull(entity)) {
                        r.setFileSize(entity.getFileSize());
                        r.setName(entity.getFileName());
                        r.setStatus(entity.getStatus());
                        // 添加新字段赋值
                        r.setCreatedName(entity.getCreatedName());
                        r.setCreatedTime(DateFormatUtils.format(entity.getCreatedTime(), "yyyy-MM-dd HH:mm:ss"));
                        r.setSummary(entity.toAbstractInfo().getSummary());
                    }
                });
            });
        }

        vo.setItems(itemVos);
        return vo;
    }


    private ItemVo itemVo (ChatRecordEntity chatRecord) {
        ItemVo itemVo = new ItemVo();
        itemVo.setSegmentId(chatRecord.getId());
        itemVo.setCreateTime(DateFormatUtils.format(chatRecord.getCreatedTime(), "yyyy-MM-dd HH:mm:SS"));
        itemVo.setContent(chatRecord.getMsg());
        itemVo.setRole(chatRecord.getRole());
        if (JsonUtil.isJsonArray(chatRecord.getExtendInfo())) {
            itemVo.setRefs(Safes.of(JSONArray.parseArray(chatRecord.getExtendInfo(), RefsParam.class)).stream().distinct()
                    .collect(Collectors.toList()));
        } else {
            ChatRecordExtendInfo extendInfo = chatRecord.toExtendInfo();
            itemVo.setRefs(Safes.of(extendInfo.getRefs()).stream().distinct().collect(Collectors.toList()));
            itemVo.setThinkingElapsedSecs(Optional.ofNullable(extendInfo.getThinkingElapsedSecs()).orElse(0L));
        }
        itemVo.setGroupId(chatRecord.getGroupCode());
        itemVo.setChatCode(chatRecord.getChatCode());
        itemVo.setStatus(chatRecord.getStatus());
        return itemVo;
    }


    @Override
    public QAMsg getQAMsg (String msgId) {
        if (StringUtils.isBlank(msgId)) return null;

        ChatRecordEntity chatRecordA = chatRecordService.getById(msgId);
        Yssert.isFalse(Objects.isNull(chatRecordA), "回答不存在，msgId:" + msgId);
        Yssert.isFalse(chatRecordA.getRole().equals("user"), "不允许的消息类型，msgId：" + msgId);

        ChatRecordEntity chatRecordQ = chatRecordService.getOne(new LambdaQueryWrapper<ChatRecordEntity>().eq(ChatRecordEntity::getGroupCode, chatRecordA.getGroupCode()).eq(ChatRecordEntity::getRole, "user"));
        Yssert.isFalse(Objects.isNull(chatRecordQ), "此回答不存在对应的问题，msgId：" + msgId);

        QAMsg qaMsg = new QAMsg();
        qaMsg.setInput(chatRecordQ.getMsg());

        OutputMsg outputMsg = new OutputMsg();
        outputMsg.setContent(chatRecordA.getMsg());

        if (JsonUtil.isJsonArray(chatRecordA.getExtendInfo())) {
            outputMsg.setRefs(Safes.of(JSONArray.parseArray(chatRecordA.getExtendInfo(), RefsParam.class)).stream().distinct().collect(Collectors.toList()));
        } else {
            ChatRecordExtendInfo extendInfo = chatRecordA.toExtendInfo();
            outputMsg.setRefs(Safes.of(extendInfo.getRefs()).stream().distinct().collect(Collectors.toList()));
        }
        qaMsg.setOutput(outputMsg);

        return qaMsg;
    }


    @Override
    public boolean verify (String chatCode, String groupCode) {
        return StringUtils.isBlank(groupCode) || chatRecordService.count(new LambdaQueryWrapper<ChatRecordEntity>().eq(ChatRecordEntity::getChatCode, chatCode).eq(ChatRecordEntity::getGroupCode, groupCode)) > 0;
    }


    @Override
    public List<String> modelInputs (String chatCode, Integer limit) {
        Yssert.notEmpty(chatCode, "会话编码为空。");
        limit = Objects.isNull(limit) || limit <= 0 ? 3 : limit;
        return Safes.of(chatRecordService.list(new LambdaQueryWrapper<ChatRecordEntity>().eq(ChatRecordEntity::getChatCode, chatCode)
                        .orderByDesc(ChatRecordEntity::getUpdatedTime)
                        .eq(ChatRecordEntity::getRole, "user")
                        .last("limit " + limit)
                        .select(ChatRecordEntity::getModelData)))
                .stream()
                .filter(Objects::nonNull)
                .map(ChatRecordEntity::getModelData)
                .collect(Collectors.toList());
    }


    @Override
    public ChatMessage getRecord (String msgId) {
        ChatRecordEntity record = chatRecordService.getById(msgId);
        Yssert.notNull(record, "不存在msgId为：" + msgId + "的记录");
        ChatMessage chatMessage = new ChatMessage();
        BeanUtils.copyProperties(record, chatMessage);

        return chatMessage;
    }


    @Override
    public ItemVo msgDetailById (String msgId) {
        ChatRecordEntity record = chatRecordService.getById(msgId);
        Yssert.notNull(record, "不存在msgId为：" + msgId + "的记录");
        return itemVo(record);
    }

}
