package com.ksyun.gov.titanic.userportal.client.ai.dify.client;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.Forest;
import com.dtflys.forest.http.ForestResponse;
import com.ksyun.gov.fd.common.json.JsonUtil;
import com.ksyun.gov.fd.common.lang.Safes;
import com.ksyun.gov.fd.common.lang.Yssert;
import com.ksyun.gov.titanic.userportal.client.ai.AIClient;
import com.ksyun.gov.titanic.userportal.client.ai.annotation.AIClientSelector;
import com.ksyun.gov.titanic.userportal.client.ai.dify.config.DifyConfig;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.*;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.client.AddLLMKnowledgeParam;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.client.UpdateMetadataListInfoParam;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.client.UpdateMetadataParam;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.titanic.ChatReq;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.titanic.ChatRes;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.titanic.WorkRunReq;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.upload.*;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.upload.RerankingModel;
import com.ksyun.gov.titanic.userportal.client.ai.enums.AIClientTypeEnum;
import com.ksyun.gov.titanic.userportal.client.ai.util.RemoteUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.File;
import java.util.*;
import java.util.concurrent.TimeUnit;

@AIClientSelector(type = AIClientTypeEnum.AI_CLIENT_DIFY)
@Component("aiClientDIFY")
@Slf4j
public class DifyAIClient implements AIClient {
    /**
     * 用户的标识信息id
     */
    private static final String KEY_DIFY_METADATA_AUTHORITY_NAME = "authority_user_id";
    /**
     * 用户的标签信息
     */
    private static final String KEY_DIFY_KBCODE_NAME = "kbcode";
    private static final String KEY_DIFY_METADATA_AUTHORITY_TYPE = "string";
    private static final int KET_KNOWLEDGE_PAGE = 1;
    private static final int KET_KNOWLEDGE_LIMIT = 600;

    /**
     * 元数据内置元数据id
     */
    private static final String KEY_DOC_METADATA_BUILT_IN_ID_VALUE = "built-in";

    private String baseUrl;
    private String chatAPIKey;
    private String datasetKey;
    private String workFlowAPIKey;
    private OkHttpClient okHttpClient;
    @Autowired
    private DifyConfig difyConfig;

    @Bean
    public DifyAIClient init() {
        this.baseUrl = difyConfig.BASE_URL;
        this.chatAPIKey = difyConfig.CHAT_API_KEY;
        this.datasetKey = difyConfig.DATASET_KEY;
        this.workFlowAPIKey = difyConfig.WORK_FLOW_API_KEY;
        this.okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(difyConfig.CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(difyConfig.WRITE_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(difyConfig.READ_TIMEOUT, TimeUnit.SECONDS)
                .build();
        return this;
    }

    @Override
    public ChatRes chat(ChatReq request) {
        return null;
    }

    @Override
    public String getKeyMetadataAuthorityName() {
        return KEY_DIFY_METADATA_AUTHORITY_NAME;
    }

    @Override
    public void chatStream(ChatReq request, EventSourceListener resultListener, String traceId) {
        OkHttpClient httpClient = this.okHttpClient;
        ChatParam chatParam = toDifyChatParam(request);
        chatParam.setResponse_mode("streaming");

        String requestBody = JsonUtil.of(chatParam);
        String reqUrl = baseUrl + "/chat-messages";
        Request callRequest = new Request.Builder()
                .url(reqUrl)
                .headers(Headers.of("Authorization", "Bearer " + chatAPIKey))
                .post(RequestBody.create(MediaType.parse("application/json"), requestBody))
                .build();

        log.info("traceId:{}，chatStream invoke:{} body:{} ", traceId, callRequest.toString(), requestBody);
        EventSource.Factory factory = EventSources.createFactory(httpClient);
        EventSourceListener okHttpSSEListener = resultListener;
        factory.newEventSource(callRequest, okHttpSSEListener);
    }


    @Override
    public void workflowsRun(WorkRunReq request, EventSourceListener resultListener, String traceId) {
        OkHttpClient httpClient = this.okHttpClient;
        WorkflowsRunParam workflowsRunParam = toDifyWorkflowsRunParam(request);
        workflowsRunParam.setResponse_mode("streaming");

        String requestBody = JsonUtil.of(workflowsRunParam);
        String reqUrl = baseUrl + "/workflows/run";
        Request callRequest = new Request.Builder()
                .url(reqUrl)
                .headers(Headers.of("Authorization", "Bearer " + workFlowAPIKey))
                .post(RequestBody.create(MediaType.parse("application/json"), requestBody))
                .build();

        log.info("traceId:{},chatStream invoke:{} body:{} ", traceId, callRequest.toString(), requestBody);
        EventSource.Factory factory = EventSources.createFactory(httpClient);
        EventSourceListener okHttpSSEListener = resultListener;
        factory.newEventSource(callRequest, okHttpSSEListener);
    }


    /**
     * 对象转换
     *
     * @param req
     * @return
     */
    private ChatParam toDifyChatParam(ChatReq req) {
        ChatParam chatParam = new ChatParam();
        Optional.ofNullable(Safes.last(req.getMessages())).ifPresent(message -> {
            chatParam.setQuery(message.getContent());
        });

        chatParam.setInputs(Objects.isNull(req.getInputs()) ? new HashMap<>() : req.getInputs());
        chatParam.setResponse_mode("blocking");
        chatParam.setUser(req.getOperatorUid());
        chatParam.setConversation_id(req.getChatId());
        // chatParam.setRefs(Optional.ofNullable(Safes.first(req.getRefKnowledgeList())).stream().map(RefKnowledge::getKbCode).collect(Collectors.toList()));
        return chatParam;
    }

    //
    private WorkflowsRunParam toDifyWorkflowsRunParam(WorkRunReq req) {
        WorkflowsRunParam workflowsRunParam = new WorkflowsRunParam();
        workflowsRunParam.setInputs(Objects.isNull(req.getInputs()) ? new HashMap<>() : req.getInputs());
        workflowsRunParam.setResponse_mode("streaming");
        workflowsRunParam.setUser(req.getOperatorUid());
        return workflowsRunParam;
    }


    private ChatRes convertToChatRes(ChatResp chatResp) {
        ChatRes chatRes = new ChatRes();
        return chatRes;
    }


    public DifyApi getApi() {
        return Forest.client(DifyApi.class);
    }


    /**
     * 创建知识库
     *
     * @param
     * @return
     */
    public AppConfigInfoResp appConfigInfo() {
        return (AppConfigInfoResp) RemoteUtil.invokeThrowEx(() -> getApi().queryAppConfigInfo(baseUrl, chatAPIKey), "调用Dify知识库创建接口失败");
    }


    /**
     * 创建知识库
     *
     * @param name
     * @return
     */
    public DatasetCreateResp createKb(String name) {
        DatasetCreateReq datasetCreateReq = new DatasetCreateReq();
        datasetCreateReq.setName(name);
        return (DatasetCreateResp) RemoteUtil.invokeThrowEx(() -> getApi().createKb(baseUrl, datasetKey, datasetCreateReq), "调用Dify知识库创建接口失败");
    }

    /**
     * 获取知识库的id信息
     *
     * @param addLLMKnowledgeParam tag 关键字
     * @return
     */
    @Override
    public DatasetsInfoResp.DataSetInfo findKnowledgeIdByTagKey(AddLLMKnowledgeParam addLLMKnowledgeParam) {
        List<TagInfoResp> tagInfoListResps = findTagListByKeyWord(addLLMKnowledgeParam.getItemTagName());
        if (tagInfoListResps == null || tagInfoListResps.size() < 1) {
            log.error("traceId:{},findKnowledgeByTag result is null,keywork:{}", addLLMKnowledgeParam.getTraceId(), addLLMKnowledgeParam.getItemTagName());
            throw new RuntimeException("标签获取异常");
        }
        String tagId = tagInfoListResps.get(0).getId();
        if (StringUtils.isEmpty(tagId)) {
            log.error("traceId:{},findKnowledgeByTag tagId is null,keywork:{}", addLLMKnowledgeParam.getTraceId(), addLLMKnowledgeParam.getItemTagName());
            throw new RuntimeException("标签id为空");
        }
        DatasetsInfoResp datasetsInfoRespAll = findKnTagListByTagIds(KET_KNOWLEDGE_PAGE, KET_KNOWLEDGE_LIMIT, false, tagId);
        /**
         * 有效值获取
         * 第一:是否为空值
         * 第二：是否有关键的元数据信息
         */
        DatasetsInfoResp datasetsInfoResp = getValidDatasetsInfoRespt(addLLMKnowledgeParam, datasetsInfoRespAll, tagId);
        if (datasetsInfoResp == null || datasetsInfoResp.getData().isEmpty()) {
            log.error("traceId:{},findKnowledgeByTag by metadatakey:{},result is null,tagId:{},metadatakey:{}", addLLMKnowledgeParam.getTraceId(), KEY_DIFY_METADATA_AUTHORITY_NAME, tagId);
            throw new RuntimeException("查询知识库异常,未查询到知识库信息,tageId:" + tagId);
        }
        if (StringUtils.isEmpty(addLLMKnowledgeParam.getAuthorityUserId()) || datasetsInfoResp.getData().size() == 1) {
            DatasetsInfoResp.DataSetInfo dataSetInfo = datasetsInfoResp.getData().get(0);
            log.info("traceId:{},findKnowledgeId finde index user:{}, index 0, tagId:{},datasetId:{}", addLLMKnowledgeParam.getTraceId(), addLLMKnowledgeParam.getAuthorityUserId(), tagId, JSONObject.toJSON(dataSetInfo));
            return dataSetInfo;
        }
        int index = Math.floorMod(addLLMKnowledgeParam.getAuthorityUserId().hashCode(), datasetsInfoResp.getData().size());
        log.info("trceId:{},findKnowledgeId finde index user:{}, index{}, tagId:{}", addLLMKnowledgeParam.getTraceId(), addLLMKnowledgeParam.getAuthorityUserId(), index, tagId);
        DatasetsInfoResp.DataSetInfo dataSetInfo = datasetsInfoResp.getData().get(index);
        log.info("trceId:{},findKnowledgeId  result datasetId:{}", addLLMKnowledgeParam.getTraceId(), JSONObject.toJSON(dataSetInfo));
        return dataSetInfo;
    }

    private DatasetsInfoResp getValidDatasetsInfoRespt(AddLLMKnowledgeParam addLLMKnowledgeParam, DatasetsInfoResp datasetsInfoResp, String tagId) {

        if (datasetsInfoResp == null || datasetsInfoResp.getData().isEmpty()) {
            log.error("traceId:{},findKnowledgeByTag result is null, tagId:{}", addLLMKnowledgeParam.getTraceId(), tagId);
            throw new RuntimeException("查询知识库异常,未查询到知识库信息,tageId:" + tagId);
        }
        /**
         * 检查是否包含元数据信息
         */
        for (int i = 0; i < datasetsInfoResp.getData().size(); i++) {
            DatasetsInfoResp.DataSetInfo docMetadataDTOList1 = datasetsInfoResp.getData().get(i);
            List<DatasetsInfoResp.DataSetInfo.DocMetadataDTO> docMetadataDTOList = docMetadataDTOList1.getDocMetadata().stream().
                    filter(metadata -> addLLMKnowledgeParam.getMateDataMap().containsKey(metadata.getName()))
                    .toList();
            if (docMetadataDTOList == null || docMetadataDTOList.isEmpty()) {
                datasetsInfoResp.getData().remove(i);
            }
        }
        return datasetsInfoResp;
    }


    public FindMetadataResp queryMetadataInfo(String datasetId) {
        return (FindMetadataResp) RemoteUtil.invokeThrowExNotResultSuccess(() -> getApi().queryMetadataInfo(baseUrl, datasetKey, datasetId), "调用Dify获取元数据信息接口失败");
    }

    @Override
    public boolean updateAllMetadataInfo(UpdateMetadataListInfoParam param, String traceId) {
        /**
         * 验证
         */
        traceId = validateUpdateAllMetadataInfo(param, traceId);
        /**
         * 处理
         * 1.放到对应的map
         */
        handleUpdataMetaDataInfo(param, traceId);
        /**
         * build
         */
        List<MetadataUpdateParam.OperationDataDTO.MetadataListDTO> metadataListDTOS = buildAddMeta(param);

        /**
         * 新增元数据管理
         */
        MetadataUpdateParam metadataUpdateParam = MetadataUpdateParam.builder()
                .operation_data(Collections.singletonList(  // 使用singletonList更高效
                        MetadataUpdateParam.OperationDataDTO.builder()
                                .document_id(param.getDocumentId())
                                .metadata_list(metadataListDTOS)
                                .build()
                ))
                .build();
        RemoteUtil.invokeVoidThrowEx(() -> getApi().updateMetadata(baseUrl, datasetKey, param.getDatasetId(), metadataUpdateParam), "调用Dify更新元数据信息接口失败");
        return true;
    }

    private void handleUpdataMetaDataInfo(UpdateMetadataListInfoParam param, String traceId) {
        HashMap<String, String> notMetaDataIdMap = param.getNotMetadataIdMap();
        if (notMetaDataIdMap == null) {
            notMetaDataIdMap = new HashMap<String, String>();
        }
        HashMap<String, UpdateMetadataParam> existsMetaDataIds = param.getExistsMetaDataIds();
        if (existsMetaDataIds == null) {
            existsMetaDataIds = new HashMap<String, UpdateMetadataParam>();
        }
        for (UpdateMetadataParam updateMetadataParam : param.getMetadataListParams()) {
            if (!StringUtils.hasLength(updateMetadataParam.getMetaDataName()) || !StringUtils.hasLength(updateMetadataParam.getMetaDataValue())) {
                log.error("traceId:{},handleUpdataMetaDataInfo metaDataName or metaDataValue not is null", traceId);
                throw new RuntimeException("元数据信息名称和值不能为空");
            }
            if (StringUtils.hasLength(updateMetadataParam.getMetaDataId())) {
                existsMetaDataIds.put(updateMetadataParam.getMetaDataName(), updateMetadataParam);
                continue;
            }
            notMetaDataIdMap.put(updateMetadataParam.getMetaDataName(), updateMetadataParam.getMetaDataValue());
        }

        param.setExistsMetaDataIds(existsMetaDataIds);
        param.setNotMetadataIdMap(notMetaDataIdMap);


    }

    private String validateUpdateAllMetadataInfo(UpdateMetadataListInfoParam param, String traceId) {
        if (traceId != null) {
            param.setTraceId(traceId);
        } else {
            traceId = param.getTraceId();
        }
        if (!StringUtils.hasLength(traceId)) {
            log.error("traceId is null");
            throw new RuntimeException("traceId不能为空");
        }

        if (param.getMetadataListParams() == null || param.getMetadataListParams().isEmpty()) {
            log.error("traceId:{},MetadataListParams is null", traceId);
            throw new RuntimeException("元数据信息不能为空");
        }

        if (!StringUtils.hasLength(param.getDatasetId())) {
            log.error("traceId:{},datasetId not is null", traceId);
            throw new RuntimeException("datasetId不能为空");
        }

        if (!StringUtils.hasLength(param.getDocumentId())) {
            log.error("traceId:{},DocumentId not is null", traceId);
            throw new RuntimeException("DocumentId不能为空");
        }
        return traceId;
    }


    public Boolean updateMetadataAllInfo(String datasetId, MetadataUpdateParam metadataUpdateParam) {
        RemoteUtil.invokeVoidThrowEx(() -> getApi().updateMetadata(baseUrl, datasetKey, datasetId, metadataUpdateParam), "调用Dify更新元数据信息接口失败");
        return true;
    }


    private List<MetadataUpdateParam.OperationDataDTO.MetadataListDTO> buildAddMeta(UpdateMetadataListInfoParam param) {
        DocumentDetailMetadataResp documentDetailMetadataResp = documentDetailMetadata(param.getDatasetId(), param.getDocumentId());
        if (documentDetailMetadataResp == null) {
            log.error("traceId:{},DocumentId document 不存在 ,dataSetId:{},documentId:{}", param.getTraceId(), param.getDatasetId(), param.getDocumentId());
            throw new RuntimeException("文档信息不存在,id:" + param.getDocumentId());
        }
        List<MetadataUpdateParam.OperationDataDTO.MetadataListDTO> metadataListDTOList = new ArrayList<>();
        HashMap<String, UpdateMetadataParam> existsMetaDataMap = param.getExistsMetaDataIds();
        HashMap<String, String> notMetadataIdMap = param.getNotMetadataIdMap();
        /**
         * 通过metadata进行赋值信息
         */
        assignmenMetadataByDocment(documentDetailMetadataResp, metadataListDTOList, existsMetaDataMap, notMetadataIdMap);


        if (existsMetaDataMap.isEmpty() && notMetadataIdMap.isEmpty()) {
            return metadataListDTOList;
        }

        /**
         * 处理新增的元数据，带metaId
         */
        handleMetadataExistsMetaDataMap(existsMetaDataMap, metadataListDTOList, param);

        /**
         * 查询相关元数据属性,没有在文件中进行新增
         */
        handleNotMetadataIdCreateAndUpdate(param, notMetadataIdMap, metadataListDTOList);
        return metadataListDTOList;
    }

    private void handleMetadataExistsMetaDataMap(HashMap<String, UpdateMetadataParam> existsMetaDataMap, List<MetadataUpdateParam.OperationDataDTO.MetadataListDTO> metadataListDTOList, UpdateMetadataListInfoParam param) {

        if (existsMetaDataMap.isEmpty()) {
            return;
        }
        for (Map.Entry<String, UpdateMetadataParam> updateMetadataParamTempEntry : existsMetaDataMap.entrySet()) {
            log.info("traceId:{}, The metadata is added based on the metadata id if the original metadata is not added, mataData:{}", param.getTraceId(), JSONObject.toJSON(updateMetadataParamTempEntry.getValue()));
            metadataListDTOList.add(MetadataUpdateParam.OperationDataDTO.MetadataListDTO.
                    builder().name(updateMetadataParamTempEntry.getValue().getMetaDataName())
                    .id(updateMetadataParamTempEntry.getValue().getMetaDataId())
                    .value(updateMetadataParamTempEntry.getValue().getMetaDataValue()).type(KEY_DIFY_METADATA_AUTHORITY_TYPE)
                    .build());
        }


    }


    /**
     * 通过文档信息赋值
     *
     * @param documentDetailMetadataResp
     * @param metadataListDTOList
     * @param existsMetaDataMap
     * @param notMetadataIdMap
     */
    private void assignmenMetadataByDocment(DocumentDetailMetadataResp documentDetailMetadataResp, List<MetadataUpdateParam.OperationDataDTO.MetadataListDTO> metadataListDTOList, HashMap<String, UpdateMetadataParam> existsMetaDataMap, HashMap<String, String> notMetadataIdMap) {

        if (documentDetailMetadataResp.getDocMetadata() == null || documentDetailMetadataResp.getDocMetadata().isEmpty()) {
            return;
        }
        for (DocumentDetailMetadataResp.DocMetadataDTO docMetadataDTO : documentDetailMetadataResp.getDocMetadata()) {
            if (docMetadataDTO.getId().equals(KEY_DOC_METADATA_BUILT_IN_ID_VALUE)) {
                continue;
            }
            String value = docMetadataDTO.getValue();
            UpdateMetadataParam metaDataParamList = existsMetaDataMap.remove(docMetadataDTO.getName());
            String notMetaIdMapValueTemp = notMetadataIdMap.remove(docMetadataDTO.getName());
            /**
             * 如有原始的进行使用原始，防止用户更新后续id有变更
             */
            if (metaDataParamList != null && metaDataParamList.getMetaDataId() != null) {
                value = metaDataParamList.getMetaDataValue();
            } else if (StringUtils.hasLength(notMetaIdMapValueTemp)) {
                value = notMetaIdMapValueTemp;
            }
            metadataListDTOList.add(MetadataUpdateParam.OperationDataDTO.MetadataListDTO.
                    builder().name(docMetadataDTO.getName()).id(docMetadataDTO.getId()).type(docMetadataDTO.getType())
                    .value(value).build());
        }


    }

    public void handleNotMetadataIdCreateAndUpdate(UpdateMetadataListInfoParam requestParam, Map<String, String> notExistsMetaDataIds, List<MetadataUpdateParam.OperationDataDTO.MetadataListDTO> metadataListDTOS) {
        /**
         * 赋值
         */
        assignmenMetadataByDatasetInfo(requestParam.getTraceId(), requestParam.getDatasetId(), notExistsMetaDataIds, metadataListDTOS);

        if (notExistsMetaDataIds.isEmpty()) {
            return;
        }
        /**
         * 还有没有元数据，进行新增
         */
        addMetadata(requestParam.getTraceId(), requestParam.getDatasetId(), notExistsMetaDataIds);
        /**
         * 重新查询然后赋值
         */
        assignmenMetadataByDatasetInfo(requestParam.getTraceId(), requestParam.getDatasetId(), notExistsMetaDataIds, metadataListDTOS);
        if (notExistsMetaDataIds.isEmpty()) {
            return;
        }

        log.error("traceId:{},handleMetadata error:not find metadata, datasetId:{},notExistsMetaDataIds：{}", requestParam.getTraceId(), requestParam.getDatasetId(), JSONObject.toJSON(notExistsMetaDataIds));
        StringJoiner msg = new StringJoiner(",");
        for (Map.Entry<String, String> entry : notExistsMetaDataIds.entrySet()) {
            msg.add(entry.getKey());
        }
        Yssert.throwEx("获取元数据信息异常,datasetId:" + requestParam.getTraceId() + ",元数据名称为:" + msg);

    }

    private void addMetadata(String traceId, String datasetId, Map<String, String> notExistsMetaDataIds) {

        if (notExistsMetaDataIds == null || notExistsMetaDataIds.size() == 0) {
            return;
        }
        notExistsMetaDataIds.entrySet().stream().forEach((notExistsMetaDataMap -> {
            AddMetadataParam addMetadataParam = new AddMetadataParam();
            addMetadataParam.setType(KEY_DIFY_METADATA_AUTHORITY_TYPE);
            addMetadataParam.setName(notExistsMetaDataMap.getKey());
            AddMetadataResp addMetadataResp = null;
            try {
                log.info("traceId:{},if the metadata cannot be queried, add it again, handleMetadata add start, addMetadata:{}", traceId, JSONObject.toJSON(addMetadataParam));
                // 重复会导致失败
                addMetadataResp = RemoteUtil.invokeThrowEx(() -> getApi().addMetadata(baseUrl, datasetKey, datasetId, addMetadataParam), "调用Dify更新元数据信息接口失败");
                log.info("traceId:{},if the metadata cannot be queried, add it again, handleMetadata end start, addMetadata:{}", traceId, JSONObject.toJSON(addMetadataParam));

            } catch (Exception e) {
                log.error("traceId:" + traceId + ",handleMetadata add addMetadata error:", e);
            }

        }));

    }

    private void assignmenMetadataByDatasetInfo(String traceId, String datasetId, Map<String, String> notExistsMetaDataIds, List<MetadataUpdateParam.OperationDataDTO.MetadataListDTO> metadataListDTOS) {
        FindMetadataResp metadataRespsList = queryMetadataInfo(datasetId);
        // String metaDataId = getMetadataId(metadataRespsList,notExistsMetaDataIds);
        if (metadataRespsList == null || metadataRespsList.getDocMetadata() == null || metadataRespsList.getDocMetadata().size() == 0) {
            log.info("traceId:{}, handleMetadataCreateAndUpdate datasetId:{}, by datasetId find metadata is null", traceId, datasetId);
            return;
        }
        for (FindMetadataResp.DocMetadataDTO metadataResp : metadataRespsList.getDocMetadata()) {
            String metaValue = notExistsMetaDataIds.remove(metadataResp.getName());
            if (StringUtils.hasLength(metaValue)) {
                log.info("traceId:{}, query the knowledge datasetId:{} base again to obtain metadata, mataData:{}", traceId, datasetId, JSONObject.toJSON(metadataResp));
                metadataListDTOS.add(MetadataUpdateParam.OperationDataDTO.MetadataListDTO.
                        builder().name(metadataResp.getName())
                        .id(metadataResp.getId())
                        .value(metaValue).type(metadataResp.getType())
                        .build());

            }
            if (notExistsMetaDataIds.isEmpty()) {
                break;
            }
        }


    }

    private String getMetadataId(List<MetadataResp> metadataRespList, Map<String, String> notExistsMetaDataIds
    ) {
        if (metadataRespList.isEmpty()) {
            return null;
        }
        for (MetadataResp metadataResp : metadataRespList) {
            if (KEY_DIFY_METADATA_AUTHORITY_NAME.equals(metadataResp)) {
                return metadataResp.getId();
            }
        }
        return null;

    }


    /**
     * 通过关键字获取标签
     *
     * @param keyword
     * @return
     */
    private List<TagInfoResp> findTagListByKeyWord(String keyword) {
        String type = "knowledge";
        return (List<TagInfoResp>) RemoteUtil.invokeListThrowEx(() -> getApi().tags(baseUrl, datasetKey, type, keyword), "调用Dify通过标签查询知识库接口失败");
    }

    /**
     * 通过标签的Id获取知识库
     *
     * @param page
     * @param limit
     * @param includeAll
     * @param tagIds
     * @return
     */
    public DatasetsInfoResp findKnTagListByTagIds(int page, int limit, boolean includeAll, String tagIds) {
        return (DatasetsInfoResp) RemoteUtil.invokeThrowEx(() -> getApi().datasets(baseUrl, datasetKey, page, limit, includeAll, tagIds), "调用Dify通过标签的Id获取知识库接口失败");
    }

    /**
     * 删除知识库
     *
     * @param dataSetId
     */
    public void delKb(String dataSetId) {
        RemoteUtil.invokeVoidThrowEx(() -> getApi().delKb(baseUrl, datasetKey, dataSetId), "调用Dify知识库删除接口");
    }


    /**
     * 创建知识库文件
     *
     * @param datasetId
     * @param file
     * @return
     */
    @Override
    public Document addKbFile(String datasetId, File file) {
        FileUploadConfig fileUploadConfig = buildFileUploadConfig();
        DatasetFileCreateResp datasetFileCreateResp = (DatasetFileCreateResp) RemoteUtil.invokeThrowEx(() -> getApi().addKbFile(datasetKey, baseUrl, datasetId, file, JSONUtil.toJsonStr(fileUploadConfig)), "调用Dify知识库文件创建接口");
        return datasetFileCreateResp.getDocument();
    }

    @Override
    public Document addKbFileV2(String datasetId, File file) {
        FileUploadConfig fileUploadConfig = buildFileUploadConfig();
        ForestResponse<DatasetFileCreateResp> datasetFileCreateResp = getApi().addKbFile(datasetKey, baseUrl, datasetId, file, JSONUtil.toJsonStr(fileUploadConfig));
        return datasetFileCreateResp.getResult().getDocument();
    }

    public DocumentDetailMetadataResp documentDetailMetadata(String datasetId, String documentId) {
        return RemoteUtil.invokeThrowEx(() -> getApi().documentDetailMetadata(baseUrl, datasetKey, datasetId, documentId), "调用Dify知识库文件详情查询元数据接口");
    }

    /**
     * 返回全部信息
     *
     * @param datasetId
     * @param file
     * @return
     */
    @Override
    public DatasetFileCreateResp addKbFileReturnInfo(String datasetId, File file) {
        FileUploadConfig fileUploadConfig = buildFileUploadConfig();
        DatasetFileCreateResp datasetFileCreateResp = (DatasetFileCreateResp) RemoteUtil.invokeThrowEx(() -> getApi().addKbFile(datasetKey, baseUrl, datasetId, file, JSONUtil.toJsonStr(fileUploadConfig)), "调用Dify知识库文件创建接口");
        return datasetFileCreateResp;
    }

    private FileUploadConfig buildFileUploadConfig() {
        FileUploadConfig fileUploadConfig = new FileUploadConfig();
        fileUploadConfig.setIndexing_technique("high_quality");
        ProcessRule processRule = new ProcessRule();
        processRule.setMode("automatic");
        fileUploadConfig.setProcess_rule(processRule);
        Rule rule = new Rule();
        List<PreProcessingRule> list = new ArrayList<>();
        PreProcessingRule preProcessingRule1 = new PreProcessingRule();
        preProcessingRule1.setId("remove_extra_spaces");
        preProcessingRule1.setEnabled(true);
        list.add(preProcessingRule1);
        PreProcessingRule preProcessingRule2 = new PreProcessingRule();
        preProcessingRule2.setId("remove_urls_emails");
        preProcessingRule2.setEnabled(true);
        list.add(preProcessingRule2);
        rule.setPre_processing_rules(list);

        Segmentation segmentation = new Segmentation();
        segmentation.setSeparator("\n\n");
        segmentation.setMax_tokens("500");
        rule.setSegmentation(segmentation);
        processRule.setRules(rule);
        /**
         * 检索模式
         */
        RetrievalModel retrievalModel = new RetrievalModel();
        retrievalModel.setSearch_method("hybrid_search");
        retrievalModel.setReranking_enable(true);
        RerankingModel rerankingModel = new RerankingModel();
        rerankingModel.setReranking_provider_name("bge-reranker-large");
        rerankingModel.setReranking_model_name("langgenius/xinference/xinference");


        retrievalModel.setReranking_model(rerankingModel);
        retrievalModel.setTop_k(2);
        retrievalModel.setScore_threshold_enabled(false);
        retrievalModel.setScore_threshold(0.0F);
        fileUploadConfig.setRetrieval_model(retrievalModel);
        fileUploadConfig.setEmbedding_model("bge-large-zh-v1.5");
        fileUploadConfig.setEmbedding_model_provider("langgenius/xinference/xinference");
        return fileUploadConfig;
    }


    /**
     * 删除知识库文件
     *
     * @param datasetId
     * @param documentId
     */
    @Override
    public void delKbFile(String datasetId, String documentId, String traceId) {
        if (org.apache.commons.lang.StringUtils.isEmpty(datasetId) || org.apache.commons.lang.StringUtils.isEmpty(documentId) || org.apache.commons.lang.StringUtils.isEmpty(traceId)) {
            log.error("traceId:{},delKbFile request is null,datasetId:{},documentId:{},documentId:{}", traceId, datasetId, documentId);
            throw new RuntimeException("请求参数异常,不能为空");
        }
        log.info("traceId:{},delKbFile start datasetId:{},documentId:{},userId:{}", traceId, datasetId, documentId);
        delKbFileAPI(datasetId, documentId);
        log.info("traceId:{}, delKbFile end datasetId:{},documentId:{},userId:{}", traceId, datasetId, documentId);

    }

    @Override
    public void batchDeleteDocument(String datasetId, Set<String> documentIds, String traceId) {

        if (!StringUtils.hasLength(datasetId) || !StringUtils.hasLength(traceId)) {
            log.error("traceId:{},batchDeleteDocument request is null,datasetId:{},documentId:{},documentId:{}", traceId, datasetId, documentIds);
            throw new RuntimeException("请求参数异常,不能为空");
        }

        if (documentIds == null || documentIds.isEmpty()) {
            log.error("traceId:{},batchDeleteDocument documentId is null,datasetId:{}", traceId, datasetId);
            throw new RuntimeException("请求参数异常,文档ID不能为空");
        }
        /**
         * 有使用方控制这个地方只是打印日志信息
         */
        if (documentIds.size() > 10) {
            log.error("traceId:{},batchDeleteDocument Delete no more than 20 files in a batch,datasetId:{},documentIds size:{}", traceId, documentIds.size());
        }
        log.info("traceId:{},batchDeleteDocument start datasetId:{},documentId:{},userId:{}", traceId, datasetId, JSONObject.toJSON(documentIds));
        batchDeleteDocument(datasetId, documentIds);
        log.info("traceId:{}, batchDeleteDocument end datasetId:{},documentId:{},userId:{}", traceId, datasetId, JSONObject.toJSON(documentIds));

    }


    private void delKbFileAPI(String datasetId, String documentId) {
        RemoteUtil.invokeVoidThrowEx(() -> getApi().delKbFile(baseUrl, datasetKey, datasetId, documentId), "调用Dify知识库文件删除接口");
    }

    private void batchDeleteDocument(String datasetId, Set<String> documentIds) {
        RemoteUtil.invokeVoidThrowEx(() -> getApi().batchDeleteDocument(baseUrl, datasetKey, datasetId, documentIds), "调用Dify知识库文件删除接口");
    }


    /**
     * 知识库新增
     *
     * @param addLLMKnowledgeParam
     * @param traceId
     * @param file
     * @return AddLLMKnowledgeMode
     */

    @Override
    public AddLLMKnowledgeMode addLLMKnowledge(AddLLMKnowledgeParam addLLMKnowledgeParam, String traceId, File file,String setId) {
        traceId = HandleAddLlMKnowledgePame(addLLMKnowledgeParam, traceId);
        /**验证***/
        validateAddLLMKnowledge(addLLMKnowledgeParam, file, traceId);
        /** 获取知识库信息*/
        //        DatasetsInfoResp.DataSetInfo dataSetInfo = getKnowledgeId(addLLMKnowledgeParam);
        //        String dataSetId = dataSetInfo.getId();
        DatasetsInfoResp.DataSetInfo dataSetInfo = new DatasetsInfoResp.DataSetInfo();
        String dataSetId = setId;
        // 创建文件信息
        Document fileDocument = addKbFile(dataSetId, file);
        String documentId = fileDocument.getId();
        AddLLMKnowledgeMode addLLMKnowledgeMode = new AddLLMKnowledgeMode();
        /**处理元数据管理*/
        // handleMetadataUpdate(addLLMKnowledgeMode, addLLMKnowledgeParam, dataSetId, documentId, fileDocument, dataSetInfo);
        addLLMKnowledgeMode.setDataSetId(dataSetId);
        addLLMKnowledgeMode.setDocumentId(documentId);
        return addLLMKnowledgeMode;
    }

    private String HandleAddLlMKnowledgePame(AddLLMKnowledgeParam addLLMKnowledgeParam, String traceId) {
        /***处理元数据默认信息**/
        HashMap<String, String> map = addLLMKnowledgeParam.getMateDataMap();
        if (map == null) {
            map = new HashMap<String, String>();
        }
        map.put(KEY_DIFY_METADATA_AUTHORITY_NAME, addLLMKnowledgeParam.getAuthorityUserId());
        map.put(KEY_DIFY_KBCODE_NAME, addLLMKnowledgeParam.getKbcodeMetaDataKey());
        addLLMKnowledgeParam.setMateDataMap(map);
        if (traceId != null) {
            addLLMKnowledgeParam.setTraceId(traceId);
            return traceId;
        }
        if (StringUtils.hasLength(addLLMKnowledgeParam.getTraceId())) {
            return addLLMKnowledgeParam.getTraceId();
        }
        return null;
    }


    private void validateAddLLMKnowledge(AddLLMKnowledgeParam addLLMKnowledgeParam, File file, String traceId) {
        if (addLLMKnowledgeParam == null) {
            log.error("traceId:{},validateAddLLMKnowledge addLLMKnowledgeParam is null", traceId);
            throw new RuntimeException("参数不能为空");
        }
        if (!StringUtils.hasLength(traceId)) {
            log.error("traceId:{},validateAddLLMKnowledge traceId is null", traceId);
            throw new RuntimeException("traceId不能为空");
        }
        if (!StringUtils.hasLength(addLLMKnowledgeParam.getItemTagName()) || !StringUtils.hasLength(addLLMKnowledgeParam.getAuthorityUserId())) {
            log.error("traceId:{},addLLMKnowledge error: itemTagName:{} or authorityUserId:{} is null", traceId, addLLMKnowledgeParam.getItemTagName(), addLLMKnowledgeParam.getAuthorityUserId());
            throw new RuntimeException("业务标签或者权限用户ID不能为空");
        }

        if (!StringUtils.hasLength(addLLMKnowledgeParam.getKbcodeMetaDataKey())) {
            log.error("traceId:{},validateAddLLMKnowledge tatKey is null", traceId);
            throw new RuntimeException("tatKey不能为空");
        }
        if (file == null) {
            log.error("traceId:{},addLLMKnowledge error: file is null", traceId);
            throw new RuntimeException("文件不能为空");
        }
        if (!file.exists()) {
            log.error("traceId:{},addLLMKnowledge error: file is not exists", traceId);
            throw new RuntimeException("文件不存在: " + file.getPath());
        }
    }


    private MetadataUpdateParam handleMetadataUpdate(AddLLMKnowledgeMode addLLMKnowledgeMode, AddLLMKnowledgeParam addLLMKnowledgeParam, String dataSetId, String documentId, Document fileDocument, DatasetsInfoResp.DataSetInfo dataSetInfo) {
        MetadataUpdateParam metadataUpdateParam = handleMetadataUpdateParam(addLLMKnowledgeMode, addLLMKnowledgeParam, documentId, fileDocument, dataSetInfo);
        updateMetadataAllInfo(dataSetId, metadataUpdateParam);
        return metadataUpdateParam;
    }

    private DatasetsInfoResp.DataSetInfo getKnowledgeId(AddLLMKnowledgeParam addLLMKnowledgeParam) {
        DatasetsInfoResp.DataSetInfo dataSetInfo = findKnowledgeIdByTagKey(addLLMKnowledgeParam);
        if (dataSetInfo == null) {
            log.error("traceId:{},addLLMKnowledge error: findKnowledgeIdByTagKey is null,itemTagName:{},authorityUserId{}", addLLMKnowledgeParam.getTraceId(),
                    JSONObject.toJSON(addLLMKnowledgeParam));
            throw new RuntimeException("检查是否创建知识库集,知识库集名称:" + addLLMKnowledgeParam.getItemTagName());
        }
        if (dataSetInfo.getDocMetadata() == null || dataSetInfo.getDocMetadata().size() == 0) {
            log.error("traceId:{}, addLLMKnowledge error: getDocMetadata or getDocMetadata is null,itemTagName:{},authorityUserId{}", addLLMKnowledgeParam.getTraceId(),
                    JSONObject.toJSON(addLLMKnowledgeParam));
            throw new RuntimeException("检查是否创建元数据");

        }
        return dataSetInfo;
    }

    private MetadataUpdateParam handleMetadataUpdateParam(AddLLMKnowledgeMode addLLMKnowledgeResult, AddLLMKnowledgeParam addLLMKnowledgeParam, String documentId, Document fileDocument, DatasetsInfoResp.DataSetInfo dataSetInfo) {
        List<DatasetsInfoResp.DataSetInfo.DocMetadataDTO> docMetadataDTOList = dataSetInfo.getDocMetadata();
        List<MetadataUpdateParam.OperationDataDTO.MetadataListDTO> metadataOperationDataDTO = new ArrayList<>();
        HashMap<String, String> notExistMetadata = setMetadataOperationDataDTO(addLLMKnowledgeResult, addLLMKnowledgeParam, fileDocument, metadataOperationDataDTO);
        if (notExistMetadata != null && notExistMetadata.isEmpty()) {
            return MetadataUpdateParam.builder().operation_data(
                    Collections.singletonList(MetadataUpdateParam.OperationDataDTO.builder()
                            .metadata_list(metadataOperationDataDTO)
                            .document_id(documentId).build())
            ).build();
        }
        /**
         * 未有元数据
         */
        List<AddLLMKnowledgeMetaDataMode> metaListResult = getKowLedgeMetaDataList(addLLMKnowledgeResult);

        for (DatasetsInfoResp.DataSetInfo.DocMetadataDTO docMetadataDTO : docMetadataDTOList) {
            String metadataVaule = notExistMetadata.remove(docMetadataDTO.getName());
            if (StringUtils.hasLength(metadataVaule)) {
                metadataOperationDataDTO.add(MetadataUpdateParam.OperationDataDTO.MetadataListDTO
                        .builder().
                        id(docMetadataDTO.getId())
                        .name(docMetadataDTO.getName())
                        .type(docMetadataDTO.getType())
                        .value(metadataVaule).build());
                setAddLLMKnowledgeModeList(metaListResult, docMetadataDTO.getId(), docMetadataDTO.getName());
            }
        }
        if (!notExistMetadata.isEmpty()) {
            log.error("traceId:{},addLLMKnowledge error:Knowledge is not tag,keyMetadataAuthorityName:{}", addLLMKnowledgeParam.getTraceId(), JSONObject.toJSON(notExistMetadata));
            throw new RuntimeException("知识库未标注个人知识库元数据,不存在key:" + JSONObject.toJSON(notExistMetadata));
        }


        return MetadataUpdateParam.builder().operation_data(
                Collections.singletonList(MetadataUpdateParam.OperationDataDTO.builder()
                        .metadata_list(metadataOperationDataDTO)
                        .document_id(documentId).build())
        ).build();
    }


    private HashMap<String, String> setMetadataOperationDataDTO(AddLLMKnowledgeMode addLLMKnowledgeMode, AddLLMKnowledgeParam addLLMKnowledgeParam, Document fileDocument, List<MetadataUpdateParam.OperationDataDTO.MetadataListDTO> metadataOperationDataDTO) {
        HashMap<String, String> metaDataMap = addLLMKnowledgeParam.getMateDataMap();
        if (fileDocument.getDocMetadata() == null || fileDocument.getDocMetadata().isEmpty()) {
            log.info("setMetadataOperationDataDTO DocMetadata is null");
            return metaDataMap;
        }
        List<AddLLMKnowledgeMetaDataMode> metaListResult = getKowLedgeMetaDataList(addLLMKnowledgeMode);
        for (Document.DocMetadataDTO docMetadataDTO : fileDocument.getDocMetadata()) {
            if (KEY_DOC_METADATA_BUILT_IN_ID_VALUE.equals(docMetadataDTO.getId())) {
                continue;
            }
            String docMetadataValue = docMetadataDTO.getValue();
            String metaDataKey = metaDataMap.remove(docMetadataDTO.getName());
            if (StringUtils.hasLength(metaDataKey)) {
                docMetadataValue = metaDataKey;
                /***返回结果请求使用**/
                setAddLLMKnowledgeModeList(metaListResult, docMetadataDTO.getId(), docMetadataDTO.getName());

            }
            metadataOperationDataDTO.add(MetadataUpdateParam.OperationDataDTO.MetadataListDTO
                    .builder().
                    id(docMetadataDTO.getId())
                    .name(docMetadataDTO.getName())
                    .type(docMetadataDTO.getType())
                    .value(docMetadataValue).build());
        }
        return metaDataMap;
    }

    private List<AddLLMKnowledgeMetaDataMode> getKowLedgeMetaDataList(AddLLMKnowledgeMode addLLMKnowledgeMode) {
        List<AddLLMKnowledgeMetaDataMode> metaListResult = addLLMKnowledgeMode.getMetaDataList();
        if (metaListResult == null) {
            metaListResult = new ArrayList<AddLLMKnowledgeMetaDataMode>();
            addLLMKnowledgeMode.setMetaDataList(metaListResult);
        }
        return metaListResult;
    }

    private void setAddLLMKnowledgeModeList(List<AddLLMKnowledgeMetaDataMode> metaListResult, String id, String name) {
        AddLLMKnowledgeMetaDataMode addLLMKnowledgeMetaDataMode = new AddLLMKnowledgeMetaDataMode();
        addLLMKnowledgeMetaDataMode.setMetaDataId(id);
        addLLMKnowledgeMetaDataMode.setMetaDataName(name);
        metaListResult.add(addLLMKnowledgeMetaDataMode);
    }


}
