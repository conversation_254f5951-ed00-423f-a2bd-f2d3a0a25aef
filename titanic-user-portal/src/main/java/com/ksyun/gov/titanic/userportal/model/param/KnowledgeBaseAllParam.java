package com.ksyun.gov.titanic.userportal.model.param;

import com.ksyun.gov.fd.common.biz.PageReq;
import com.ksyun.gov.titanic.userportal.common.enums.KbmConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class KnowledgeBaseAllParam {
    /**
     * 知识库类别（个人、外挂、临时）
     */
    @Schema(title = "知识库类别(个人知识库 personal、企业知识库 external、临时知识库 temp)")
    @NotEmpty(message = "知识库类别(个人知识库 personal、企业知识库 external、临时知识库 temp)")
    private String category;
}
