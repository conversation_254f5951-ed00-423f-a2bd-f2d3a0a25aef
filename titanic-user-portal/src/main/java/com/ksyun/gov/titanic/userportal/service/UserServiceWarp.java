package com.ksyun.gov.titanic.userportal.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.ksyun.gov.fd.cloud.core.context.auth.AuthContextHolder;
import com.ksyun.gov.fd.cloud.core.web.model.LoginUser;
import com.ksyun.gov.fd.cloud.redis.client.FdStrRedisClient;
import com.ksyun.gov.fd.common.biz.PageRes;
import com.ksyun.gov.fd.common.json.JsonUtil;
import com.ksyun.gov.fd.common.lang.Safes;
import com.ksyun.gov.fd.common.lang.Yssert;
import com.ksyun.gov.fd.common.service.api.auth.entity.dto.RoleDTO;
import com.ksyun.gov.fd.common.service.api.auth.entity.req.menu.BindMenuReq;
import com.ksyun.gov.fd.common.service.api.auth.entity.req.role.QueryReq;
import com.ksyun.gov.fd.common.service.api.auth.entity.req.role.RoleReq;
import com.ksyun.gov.fd.common.service.api.auth.entity.req.userBindRole.BatchBindRoleReq;
import com.ksyun.gov.fd.common.service.api.auth.entity.req.userBindRole.BindRole;
import com.ksyun.gov.fd.common.service.api.auth.entity.req.userBindRole.BindRoleReq;
import com.ksyun.gov.fd.common.service.api.auth.fegin.RoleAPI;
import com.ksyun.gov.fd.common.service.api.auth.fegin.RoleMenuAPI;
import com.ksyun.gov.fd.common.service.api.auth.fegin.UserRoleAPI;
import com.ksyun.gov.fd.common.service.api.user.entity.dto.group.Group;
import com.ksyun.gov.fd.common.service.api.user.entity.dto.group.Tenant.TenantDTO;
import com.ksyun.gov.fd.common.service.api.user.entity.dto.group.dept.Dept;
import com.ksyun.gov.fd.common.service.api.user.entity.dto.group.dept.DeptDTO;
import com.ksyun.gov.fd.common.service.api.user.entity.dto.user.DelDTO;
import com.ksyun.gov.fd.common.service.api.user.entity.dto.user.UserDetailDTO;
import com.ksyun.gov.fd.common.service.api.user.entity.dto.user.UserPageDTO;
import com.ksyun.gov.fd.common.service.api.user.entity.req.DelReq;
import com.ksyun.gov.fd.common.service.api.user.entity.req.group.dept.DeptPageReq;
import com.ksyun.gov.fd.common.service.api.user.entity.req.group.dept.DeptQueryReq;
import com.ksyun.gov.fd.common.service.api.user.entity.req.group.dept.DeptReq;
import com.ksyun.gov.fd.common.service.api.user.entity.req.group.tenant.TenantQueryReq;
import com.ksyun.gov.fd.common.service.api.user.entity.req.group.tenant.TenantReq;
import com.ksyun.gov.fd.common.service.api.user.entity.req.group.tenant.TenantUpdateReq;
import com.ksyun.gov.fd.common.service.api.user.entity.req.user.ResetReq;
import com.ksyun.gov.fd.common.service.api.user.entity.req.user.UserListReq;
import com.ksyun.gov.fd.common.service.api.user.entity.req.user.UserQueryReq;
import com.ksyun.gov.fd.common.service.api.user.entity.req.user.UserReq;
import com.ksyun.gov.fd.common.service.api.user.fegin.DeptMgtAPI;
import com.ksyun.gov.fd.common.service.api.user.fegin.TenantMgtAPI;
import com.ksyun.gov.fd.common.service.api.user.fegin.UserMgtAPI;
import com.ksyun.gov.kdmp.openapi.user.feign.AiUserFeignAPI;
import com.ksyun.gov.titanic.userportal.config.web.UserConfig;
import com.ksyun.gov.titanic.userportal.model.param.DeptChildPageParam;
import com.ksyun.gov.titanic.userportal.model.param.user.DeptParam;
import com.ksyun.gov.titanic.userportal.model.param.user.DeptQueryParam;
import com.ksyun.gov.titanic.userportal.model.param.user.ResetParam;
import com.ksyun.gov.titanic.userportal.model.param.user.TenantParam;
import com.ksyun.gov.titanic.userportal.model.param.user.TenantQueryParam;
import com.ksyun.gov.titanic.userportal.model.param.user.TenantUpdateParam;
import com.ksyun.gov.titanic.userportal.model.param.user.UserParam;
import com.ksyun.gov.titanic.userportal.model.param.user.UserQueryParam;
import com.ksyun.gov.titanic.userportal.model.vo.UserImportVo;
import com.ksyun.gov.titanic.userportal.model.vo.user.TenantVo;
import com.ksyun.gov.titanic.userportal.model.vo.user.UserDetailVo;
import com.ksyun.gov.titanic.userportal.model.vo.user.UserPageVo;
import com.ksyun.gov.titanic.userportal.utils.TmplUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 用户管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
@Service
public class UserServiceWarp {

    @Value("${fd.auth.client.keycloak.realm}")
    private String realm;

    /**
     * 用户管理
     */
    @Autowired
    private UserMgtAPI userMgtAPI;

    @Autowired
    private UserRoleAPI userRoleAPI;

    @Autowired
    private UserConfig userConfig;

    @Autowired
    private AiUserFeignAPI userFeignAPI;

    @Autowired
    private FdStrRedisClient fdStrRedisClient;

    @Autowired
    private TmplUtil tmplUtil;

    private static final String userCacheKey = "ai-user-portal:user_info_%s";

    /**
     * 账号体系切换开关
     */
    @Value("${user.enable}")
    private boolean useOthAccount;


    public String addUser (UserParam req) {
        Yssert.isTrue(isValidEmail(req.getEmail()), "邮箱码格式错误");
        Yssert.isTrue(isValidPhone(req.getPhone()), "联系号码格式错误");

        // 防止误传userId
        req.setUserId(null);

        UserReq userReq = convertUserParam(req);
        userReq.setCreateBy(req.getOperateUId());
        String userId = userMgtAPI.addUser(userReq).getSuccessData();

        // 绑定角色
        if (CollUtil.isNotEmpty(req.getRoleIds())) {
            req.setUserId(userId);
            userRoleAPI.bindRole(convertBindRoleReq(req));
        }

        return userId;
    }


    public Boolean updateUser (UserParam req) {
        UserReq userReq = convertUserParam(req);
        userReq.setUpdateBy(AuthContextHolder.getLoginUser().getUid());
        userReq.setUpdateTime(DateUtil.now());
        userMgtAPI.updateUser(userReq).getSuccessData();
        return userRoleAPI.bindRole(convertBindRoleReq(req)).getSuccessData();
    }


    private BindRoleReq convertBindRoleReq (UserParam req) {
        BindRoleReq bindRoleReq = new BindRoleReq();
        bindRoleReq.setUserId(req.getUserId());
        bindRoleReq.setRoleIds(req.getRoleIds());
        bindRoleReq.setOperateUId(req.getOperateUId());
        return bindRoleReq;
    }


    private UserReq convertUserParam (UserParam req) {
        // 操作用户 + 添加|更新的用户
        String userId = req.getUserId();
        String operateUId = req.getOperateUId();
        Map<String, UserDetailVo> map = listUser(StringUtils.isBlank(userId) ? List.of(operateUId) : List.of(operateUId, userId));

        // 更新操作
        if (StringUtils.isNotBlank(userId)) {
            UserDetailVo vo = map.get(userId);
            Yssert.notNull(vo, "用户不存在：userId：" + userId);
            // // 部门 不允许修改
            // req.setDeptId(Safes.first(vo.getDepts()).getId());
            // 用户类型 不允许修改
            req.setUserType(vo.getUserType());
            // 账户 不允许修改
            req.setAccount(vo.getAccount());
            // 用户启用状态 若不填则保持上次的选择
            req.setEnabled(Optional.ofNullable(req.getEnabled()).orElse(vo.getEnabled()));
        }

        Dept dept = deptDetail(req.getDeptId());
        // 校验
        Yssert.isTrue(Objects.nonNull(map.get(operateUId)) && map.get(operateUId).getTenantId().equals(dept.getTenantId()), "不允许跨租户用户操作");

        UserReq userReq = new UserReq();
        BeanUtil.copyProperties(req, userReq);
        userReq.setRealm(realm);
        userReq.setTenantId(dept.getTenantId());
        userReq.setTenant(dept.getTenant());
        return userReq;
    }


    public Boolean delUser (String userId, String operateUId) {
        Map<String, UserDetailVo> map = listUser(StringUtils.isBlank(userId) ? List.of(operateUId) : List.of(operateUId, userId));

        if (Objects.isNull(map.get(userId))) return true;
        // 校验
        Yssert.isTrue(map.get(operateUId).getTenantId().equals(map.get(userId).getTenantId()), "不允许跨租户删除用户");

        if (userMgtAPI.delUser(convertDelParam(List.of(userId), operateUId)).getSuccessData()) {
            BindRoleReq bindRoleReq = new BindRoleReq();
            bindRoleReq.setUserId(userId);
            bindRoleReq.setRoleIds(List.of());
            bindRoleReq.setOperateUId(operateUId);
            userRoleAPI.bindRole(bindRoleReq);
        }
        return false;
    }


    public List<DelDTO> batchDelUser (List<String> userIds, String operateUId) {
        Map<String, UserDetailVo> map = listUser(Stream.concat(userIds.stream(), Stream.of(operateUId))
                .filter(Objects::nonNull)
                .toList());

        List<DelDTO> delDTOS = userMgtAPI.batchDelUser(convertDelParam(userIds, operateUId)).getSuccessData();

        Map<String, List<String>> userMap = delDTOS.stream().collect(Collectors.groupingBy(DelDTO::getStatus, Collectors.mapping(DelDTO::getUserId, Collectors.toList())));

        List<String> suc = userMap.get("success");
        if (CollUtil.isNotEmpty(suc)) {
            BatchBindRoleReq batchBindRoleReq = new BatchBindRoleReq();
            batchBindRoleReq.setBindRoles(suc.stream().map(x -> {
                BindRole bindRole = new BindRole();
                bindRole.setUserId(x);
                bindRole.setRoleIds(List.of());
                return bindRole;
            }).toList());
            batchBindRoleReq.setOperateUId(operateUId);
            userRoleAPI.batchBindRole(batchBindRoleReq);
        }

        return delDTOS;
    }


    public UserDetailVo userDetail (String userId) {
        if (useOthAccount) {
            String auth = userConfig.getUsername() + ":" + userConfig.getPassword();
            String authorization = "Basic " + Base64.encode(auth.getBytes());
            com.ksyun.gov.kdmp.openapi.user.vo.UserDetailDTO detailDTO = userFeignAPI.getUser(authorization, userId, realm).getSuccessData();
            if (Objects.nonNull(detailDTO)) {
                UserDetailVo vo = new UserDetailVo();
                BeanUtil.copyProperties(detailDTO, vo);
                return vo;
            }
            return null;
        }

        UserDetailDTO detailDTO = userMgtAPI.userDetail(userId, realm).getSuccessData();
        if (Objects.nonNull(detailDTO)) {
            UserDetailVo vo = new UserDetailVo();
            BeanUtil.copyProperties(detailDTO, vo);
            vo.setDepts(List.of(deptDetail(detailDTO.getDeptId())));
            vo.setRoles(userRoleAPI.listRole(userId).getSuccessData());
            return vo;
        }
        return null;

    }


    public Boolean isSuperAdmin (String userId) {
        if (useOthAccount) {
            String auth = userConfig.getUsername() + ":" + userConfig.getPassword();
            String authorization = "Basic " + Base64.encode(auth.getBytes());
            com.ksyun.gov.kdmp.openapi.user.vo.UserDetailDTO detailDTO = userFeignAPI.getUser(authorization, userId, realm).getSuccessData();
            if (Objects.nonNull(detailDTO)) return detailDTO.getUserType().equals("SA");

        }

        UserDetailDTO detailDTO = userMgtAPI.userDetail(userId, realm).getSuccessData();
        if (Objects.nonNull(detailDTO)) return detailDTO.getUserType().equals("SA");

        return false;

    }


    public Map<String, UserDetailVo> listUser (List<String> userIds) {
        if (useOthAccount) {
            return Safes.of(userIds).stream().map(x -> {
                String auth = userConfig.getUsername() + ":" + userConfig.getPassword();
                String authorization = "Basic " + Base64.encode(auth.getBytes());
                com.ksyun.gov.kdmp.openapi.user.vo.UserDetailDTO detailDTO = userFeignAPI.getUser(authorization, x, realm).getSuccessData();
                if (Objects.nonNull(detailDTO)) {
                    UserDetailVo vo = new UserDetailVo();
                    BeanUtil.copyProperties(detailDTO, vo);
                    return vo;
                }
                return null;
            }).collect(Collectors.toMap(UserDetailVo::getUserId, Function.identity(), (x, y) -> x));
        }

        UserListReq listReq = new UserListReq();
        listReq.setRealm(realm);
        listReq.setUserIds(userIds);

        Map<String, List<RoleDTO>> map = userRoleAPI.batchListRole(userIds).getSuccessData();
        List<UserPageDTO> pageDTO = userMgtAPI.listUser(listReq).getSuccessData();
        return Safes.of(pageDTO).stream().map(x -> {
            if (Objects.nonNull(x)) {
                UserDetailVo vo = new UserDetailVo();
                BeanUtil.copyProperties(x, vo);
                vo.setRoles(map.get(x.getUserId()));
                return vo;
            }
            return null;
        }).collect(Collectors.toMap(UserDetailVo::getUserId, Function.identity(), (x, y) -> x));
    }

    /**
     * redis 缓存
     *
     * @param userId
     * @return
     */
    public UserDetailVo userDetailWithCache (String userId) {
        if (StrUtil.isBlank(userId)) return null;

        String key = String.format(userCacheKey, userId);
        String userJson = fdStrRedisClient.get(key);
        UserDetailVo userDetailVo = null;
        if (StrUtil.isBlank(userJson)) {
            userDetailVo = userDetail(userId);
            if (userDetailVo != null) fdStrRedisClient.set(key, JsonUtil.of(userDetailVo), 30L, TimeUnit.MINUTES);
        } else {
            userDetailVo = JsonUtil.of(userJson, UserDetailVo.class);
        }


        return userDetailVo;
    }

   /* private final LoadingCache<String, UserDetailVo> userDetailByIdCache = CacheBuilder
            .newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<String, UserDetailVo>() {
                @Override
                public UserDetailVo load (String userId) throws Exception {
                    return userDetail(userId);
                }
            });
*/

    public PageRes<UserPageVo> pageUser (UserQueryParam req) {
        UserQueryReq userQueryReq = new UserQueryReq();
        BeanUtil.copyProperties(req, userQueryReq);
        userQueryReq.setRealm(realm);

        PageRes<UserPageDTO> page = userMgtAPI.pageUser(userQueryReq).getSuccessData();

        Map<String, List<RoleDTO>> map = userRoleAPI.batchListRole(Safes.of(page.getRecords()).stream().map(UserDetailDTO::getUserId).collect(Collectors.toList())).getSuccessData();
        return PageRes.of(page.getCurrent(), page.getSize(), page.getTotal(), Safes.of(page.getRecords()).stream().map(x -> {
            UserPageVo vo = new UserPageVo();
            BeanUtil.copyProperties(x, vo);
            vo.setRoles(map.get(x.getUserId()));
            return vo;
        }).collect(Collectors.toList()));
    }

    public Boolean resetUserPwd (ResetParam req) {
        ResetReq resetReq = new ResetReq();
        BeanUtil.copyProperties(req, resetReq);
        resetReq.setRealm(realm);
        resetReq.setIsTemp(true);
        return userMgtAPI.resetUserPwd(resetReq).getSuccessData();
    }


    /**
     * 租户管理
     */
    @Autowired
    private TenantMgtAPI tenantMgtAPI;

    @Autowired
    private RoleAPI roleAPI;

    @Autowired
    private RoleMenuAPI roleMenuAPI;

    public String addTenant (TenantParam req) {
        // 获取有效期
        String validFrom = req.getValidFrom();
        String validTo = req.getValidTo();

        // 校验是否存在开始时间
        boolean hasFrom = StringUtils.isNotBlank(validFrom);
        Yssert.isFalse(hasFrom ^ StringUtils.isNotBlank(validTo), "有效期必须同时填写起止时间");

        // 校验有效期 若有效期未变更则不做校验
        DateTime today = DateUtil.date();
        if (hasFrom) {
            Yssert.isFalse(DateUtil.parse(validFrom).before(today), "新有效期开始时间不能小于当前日期");
            Yssert.isFalse(DateUtil.parse(validTo).before(today), "新有效期结束时间不能小于当前日期");
        }

        TenantReq tenantReq = new TenantReq();
        BeanUtil.copyProperties(req, tenantReq);
        String operateId = req.getOperateUId();
        tenantReq.setCreateBy(operateId);
        tenantReq.setUpdateBy(operateId);
        // 时间
        String now = DateUtil.now();
        tenantReq.setCreateTime(now);
        tenantReq.setUpdateTime(now);

        tenantReq.setRealm(realm);

        String[] au = tenantMgtAPI.addTenant(tenantReq).getSuccessData().split("@");
        String userId = au[0];
        String tenantId = au[1];


        RoleReq roleReq = new RoleReq();
        roleReq.setRoleKey("admin_" + tenantId);
        roleReq.setRoleName(req.getName() + "管理员");
        roleReq.setTenantId(tenantId);
        roleReq.setType(-1);
        roleReq.setOperateUId(operateId);
        String roleId = roleAPI.addRole(roleReq).getSuccessData();


        BindRoleReq bindRoleReq = new BindRoleReq();
        bindRoleReq.setUserId(userId);
        bindRoleReq.setRoleIds(List.of(roleId));
        bindRoleReq.setOperateUId(operateId);
        userRoleAPI.bindRole(bindRoleReq);

        BindMenuReq bindMenuReq = new BindMenuReq();
        bindMenuReq.setRoleId(roleId);
        bindMenuReq.setMenuIds(req.getMenuIds());
        bindMenuReq.setOperateUId(operateId);
        bindMenuReq.setAppCode("ai");

        roleMenuAPI.bindMenu(bindMenuReq);

        return tenantId;
    }


    public Boolean updateTenant (TenantUpdateParam req) {
        TenantUpdateReq updateReq = new TenantUpdateReq();
        BeanUtil.copyProperties(req, updateReq);
        updateReq.setRealm(realm);
        updateReq.setUpdateBy(req.getOperateUId());
        updateReq.setUpdateTime(DateUtil.now());
        return tenantMgtAPI.updateTenant(updateReq).getSuccessData();
    }


    public Boolean delTenant (String tenantId, String operateId) {
        return tenantMgtAPI.delTenant(convertDelParam(List.of(tenantId), operateId)).getSuccessData();
    }


    public PageRes<TenantVo> pageTenant (TenantQueryParam req) {
        TenantQueryReq queryReq = new TenantQueryReq();
        BeanUtil.copyProperties(req, queryReq);
        queryReq.setRealm(realm);
        PageRes<TenantDTO> page = tenantMgtAPI.pageTenant(queryReq).getSuccessData();

        return PageRes.of(page.getCurrent(), page.getSize(), page.getTotal(), Safes.of(page.getRecords()).stream().map(this::tenantVo).collect(Collectors.toList()));
    }


    public TenantVo tenantDetail (String tenantId) {
        return tenantVo(tenantMgtAPI.tenantDetail(tenantId, realm).getSuccessData());
    }


    public List<TenantVo> tenantDetails (List<String> tenantIds) {
        return Safes.of(tenantMgtAPI.tenantDetails(tenantIds, realm).getSuccessData()).stream().map(this::tenantVo).toList();
    }


    private TenantVo tenantVo (TenantDTO dto) {
        TenantVo tenantVo = new TenantVo();
        BeanUtil.copyProperties(dto, tenantVo);
        if (StringUtils.isNotBlank(dto.getUserId())) {
            tenantVo.setRoles(userRoleAPI.listRole(dto.getUserId()).getSuccessData());
        }
        return tenantVo;
    }


    /**
     * 部门管理
     */
    @Autowired
    private DeptMgtAPI deptMgtAPI;


    public String addDept (DeptParam req) {
        DeptReq deptReq = convertDeptParam(req);
        // 初始化创建|更新者
        LoginUser loginUser = AuthContextHolder.getLoginUser();
        deptReq.setCreateBy(loginUser.getUid());
        deptReq.setUpdateBy(loginUser.getUid());
        // 初始化创建|更新事件
        String now = DateUtil.now();
        deptReq.setCreateTime(now);
        deptReq.setUpdateTime(now);
        return deptMgtAPI.addDept(deptReq).getSuccessData();
    }


    public Boolean updateDept (DeptParam req) {
        DeptReq deptReq = convertDeptParam(req);
        deptReq.setUpdateBy(AuthContextHolder.getLoginUser().getUid());
        deptReq.setUpdateTime(DateUtil.now());
        return deptMgtAPI.updateDept(deptReq).getSuccessData();
    }


    public DeptReq convertDeptParam (DeptParam req) {
        DeptReq deptReq = new DeptReq();
        BeanUtil.copyProperties(req, deptReq);
        deptReq.setRealm(realm);
        return deptReq;
    }


    public Boolean delDept (String deptId, String operateUId) {
        // 校验
        Yssert.isTrue(userDetail(operateUId).getTenantId().equals(deptDetail(deptId).getTenantId()), "不允许跨租户删除部门");

        return deptMgtAPI.delDept(convertDelParam(List.of(deptId), operateUId)).getSuccessData();
    }


    public DeptDTO treeDept (DeptQueryParam req) {
        DeptQueryReq queryReq = new DeptQueryReq();
        BeanUtil.copyProperties(req, queryReq);
        queryReq.setRealm(realm);
        return deptMgtAPI.treeDept(queryReq).getSuccessData();
    }


    public Dept deptDetail (String deptId) {
        return deptMgtAPI.deptDetail(deptId, realm).getSuccessData();
    }


    private DelReq convertDelParam (List<String> ids, String operateId) {
        DelReq delReq = new DelReq();
        delReq.setIds(ids);
        delReq.setRealm(realm);
        delReq.setOperateId(operateId);
        return delReq;
    }


    /**
     * 下载用户导入模板
     *
     * @param response
     */
    public void downloadUserTmpl (HttpServletResponse response) {
        String fileName = "用户导入模版.xlsx";
        tmplUtil.downloadTmpl("public/" + fileName, fileName, response);
    }


    /**
     * 导入用户模板
     *
     * @param file
     * @param operateId
     * @param tenantId
     */
    public List<UserImportVo> importUserTmpl (MultipartFile file, String operateId, String tenantId) {
        String fileName = file.getOriginalFilename();
        String extension = fileName.substring(fileName.lastIndexOf("."));

        if (extension.equals(".xlsx")) {
            try {
                return saveBatchUserFromExcel(file.getInputStream(), operateId, tenantId);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } else {
            Yssert.throwEx("不支持的文件扩展类型" + extension);
        }
        return null;
    }


    /**
     * 批量保存Excel中的文件
     *
     * @param inputStream
     */
    private List<UserImportVo> saveBatchUserFromExcel (InputStream inputStream, String operateId, String tenantId) {
        List<UserImportVo> vos = new ArrayList<>();

        // 获取租户角色列表并构建映射
        QueryReq roleQuery = new QueryReq();
        roleQuery.setAppCode("ai");
        roleQuery.setTenantId(tenantId);
        roleQuery.setOperateUId(operateId);
        roleQuery.setPageNum(1);
        roleQuery.setPageSize(9999);
        PageRes<RoleDTO> rolesPage = roleAPI.pageRole(roleQuery).getSuccessData();
        Map<String, RoleDTO> roleNameToRoleMap = rolesPage.getRecords().stream()
                .collect(Collectors.toMap(RoleDTO::getRoleName, Function.identity(), (existing, replacement) -> existing));

        // 获取部门列表并构建映射
        List<Group> deptList = deptMgtAPI.listDeptByRealm(tenantId, realm).getSuccessData();
        Map<String, Group> deptPathToGroupMap = deptList.stream()
                .collect(Collectors.toMap(Group::getPath, Function.identity(), (existing, replacement) -> existing));

        // 获取租户名称
        Group tenantGroup = deptList.stream()
                .filter(group -> group.getId().equals(tenantId))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("未找到指定租户: " + tenantId));
        String tenantName = tenantGroup.getName();

        Map<String, List<String>> userIdToRoleIdsMap = new HashMap<>();

        try (Workbook workbook = new XSSFWorkbook(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            int startRowIndex = 2;

            for (int rowIndex = startRowIndex; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row currentRow = sheet.getRow(rowIndex);
                if (currentRow == null) continue;

                // 提取单元格数据
                String userAccount = getCellValueAsString(currentRow.getCell(0));
                String userName = getCellValueAsString(currentRow.getCell(1));
                String userSourceId = getCellValueAsString(currentRow.getCell(2));
                String userGender = getCellValueAsString(currentRow.getCell(3));
                String deptPath = getCellValueAsString(currentRow.getCell(4));
                String roleNames = getCellValueAsString(currentRow.getCell(5));
                String userEmail = getCellValueAsString(currentRow.getCell(6));
                String userPhone = getCellValueAsString(currentRow.getCell(7));

                UserImportVo importVo = buildUserImportVo(
                        userAccount, userName, userSourceId,
                        userGender, deptPath, roleNames,
                        userEmail, userPhone
                );

                // 账号检查
                if (userAccount.isBlank()) {
                    buildFailImportVo(importVo, rowIndex, "行，账号不能为空", vos);
                    continue;
                }
                if (userAccount.length() >= 20) {
                    buildFailImportVo(importVo, rowIndex, "行，账号长度不能大于20", vos);
                    continue;
                }

                // 姓名检查
                if (userName.isBlank()) {
                    buildFailImportVo(importVo, rowIndex, "行，姓名不能为空", vos);
                    continue;
                }
                if (userName.length() >= 20) {
                    buildFailImportVo(importVo, rowIndex, "行，姓名长度不能大于20", vos);
                    continue;
                }

                // 性别检查
                if (!userGender.isBlank() && !List.of("男", "女").contains(userGender)) {
                    buildFailImportVo(importVo, rowIndex, "行，性别内容不符合规范，要求在列表中选择（男，女）", vos);
                    continue;
                }

                // 部门路径验证
                String fullDeptPath = "/" + tenantName + "/" + deptPath;
                if (deptPath.isBlank()) {
                    buildFailImportVo(importVo, rowIndex, "行，部门不能为空", vos);
                    continue;
                }
                if (!deptPathToGroupMap.containsKey(fullDeptPath)) {
                    buildFailImportVo(importVo, rowIndex, "行，部门不符合规范，请与系统中的组织管理保持一致", vos);
                    continue;
                }

                // 角色验证
                boolean isRoleValid = true;
                List<String> roleIds = new ArrayList<>();

                if (!roleNames.isBlank()) {
                    String[] splitRoleNames = roleNames.split("[，,]");
                    for (String rawRoleName : splitRoleNames) {
                        String roleName = rawRoleName.trim();
                        if (roleName.isEmpty()) continue;

                        RoleDTO role = roleNameToRoleMap.get(roleName);
                        if (role == null) {
                            buildFailImportVo(importVo, rowIndex, "行，不存在名称为" + roleName + "的角色", vos);
                            isRoleValid = false;
                            break;
                        }
                        roleIds.add(role.getRoleId());
                    }
                }

                if (!isRoleValid) continue;

                // 邮箱验证
                if (!userEmail.isBlank() && !isValidEmail(userEmail)) {
                    buildFailImportVo(importVo, rowIndex, "行，邮件格式不符合规范", vos);
                    continue;
                }

                // 手机号验证
                if (!userPhone.isBlank() && !isValidPhone(userPhone)) {
                    buildFailImportVo(importVo, rowIndex, "行，手机格式不符合规范", vos);
                    continue;
                }

                // 构建用户请求
                UserReq req = new UserReq();
                req.setName(userName);
                req.setAccount(userAccount);
                req.setEmail(userEmail);
                req.setRealm(realm);
                req.setPassword("ZYPt@0613");
                req.setCreateBy(operateId);
                req.setUpdateBy(operateId);

                // 设置部门信息
                Group department = deptPathToGroupMap.get(fullDeptPath);
                req.setDeptId(department.getId());
                req.setPaths(List.of(fullDeptPath));

                req.setGender(userGender);
                req.setTenantId(tenantId);
                req.setTenant(tenantName);
                req.setUserType("S");

                // 创建用户并记录角色关联
                try {
                    String userId = userMgtAPI.addUser(req).getSuccessData();
                    if (!roleIds.isEmpty()) {
                        userIdToRoleIdsMap.put(userId, roleIds);
                    }
                } catch (Exception e) {
                    buildFailImportVo(importVo, rowIndex, "行，添加用户失败: " + e.getMessage(), vos);
                }
            }
        } catch (Exception e) {
            Yssert.throwEx(e.getMessage());
        }

        // 批量绑定角色
        if (!userIdToRoleIdsMap.isEmpty()) {
            BatchBindRoleReq req = new BatchBindRoleReq();
            req.setBindRoles(userIdToRoleIdsMap.entrySet().stream()
                    .map(entry -> {
                        BindRole bindRole = new BindRole();
                        bindRole.setUserId(entry.getKey());
                        bindRole.setRoleIds(entry.getValue());
                        return bindRole;
                    })
                    .collect(Collectors.toList()));
            req.setOperateUId(operateId);
            userRoleAPI.batchBindRole(req).getSuccessData();
        }
        return vos;
    }


    /**
     * 获取单元格的值,并将其转换为字符串
     * 空单元格返回空字符串
     * 字符串类型直接返回内容
     * 数值类型会格式化为普通数字（避免科学计数法和尾部多余的.0）
     *
     * @param cell
     * @return
     */
    private String getCellValueAsString (Cell cell) {
        if (cell == null) return "";
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                // 数值类型的单元格，直接toString会变成"123.0"或科学计数法(1.2E4)
                // 用DecimalFormat强制按普通数字格式输出，并去掉末尾不需要的".0"
                return new DecimalFormat("0").format(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }


    /**
     * 构建导入响应对象
     *
     * @param account
     * @param name
     * @param sourceId
     * @param gender
     * @param path
     * @param role
     * @param email
     * @param phone
     * @return
     */
    private UserImportVo buildUserImportVo (String account,
                                            String name,
                                            String sourceId,
                                            String gender,
                                            String path,
                                            String role,
                                            String email,
                                            String phone) {
        UserImportVo vo = new UserImportVo();
        vo.setAccount(account);
        vo.setName(name);
        vo.setSourceId(sourceId);
        vo.setGender(gender);
        vo.setPath(path);
        vo.setRole(role);
        vo.setEmail(email);
        vo.setPhone(phone);
        return vo;
    }


    /**
     * 构建导入失败对象
     *
     * @param vo
     * @param i
     * @param x
     * @param vos
     */
    private void buildFailImportVo (UserImportVo vo, int i, String x, List<UserImportVo> vos) {
        vo.setStatus("fail");
        vo.setMsg("第" + (i + 1) + x);
        vos.add(vo);
    }


    /**
     * 邮箱格式验证
     *
     * @param email
     * @return
     */
    private boolean isValidEmail (String email) {
        if (StringUtils.isBlank(email)) return true;
        String emailPattern = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
        return email.matches(emailPattern);
    }


    /**
     * 手机号格式验证
     *
     * @param phone
     * @return
     */
    private boolean isValidPhone (String phone) {
        if (StringUtils.isBlank(phone)) return true;
        String phonePattern = "^1[3-9]\\d{9}$";
        return phone.matches(phonePattern);
    }


    /**
     * 分页获取子部门列表
     *
     * @param req
     * @return
     */
    public PageRes<Dept> pageChildList (DeptChildPageParam req) {
        DeptPageReq pageReq = new DeptPageReq();
        BeanUtil.copyProperties(req, pageReq);
        pageReq.setRealm(realm);

        return deptMgtAPI.pageChildList(pageReq).getSuccessData();
    }

}
