package com.ksyun.gov.titanic.userportal.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.util.Optional;

import com.ksyun.gov.fd.cloud.core.model.entity.BaseTenantEntity;
import com.ksyun.gov.fd.common.json.JsonUtil;
import com.ksyun.gov.fd.common.lang.Safes;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.mapstruct.Mapper;

/**
 * <p>
 * 知识库文件明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("knowledge_base_file_new")
public class KnowledgeBaseFileEntity extends BaseTenantEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文件编码
     */
    private String fileCode;

    /**
     * 文件编码
     */
    private String parentCode;

    /**
     * 文件类型：file->文件，directory->文件目录
     */
    private String type;

    /**
     * 文件存储服务编码
     */
    private String ossFileCode;

    /**
     * 所属知识库编码
     */
    private String kbmCode;

    /**
     * 文件大小
     */
    private Integer fileSize;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * md5
     */
    private String md5;

    /**
     * 描述
     */
    private String remark;

    private String metadataInfo = "{}";

    private String createdName;


    /**
     * 文件摘要信息
     *
     * {
     *       "summary": "本文档详细规定了企业软件在开发阶段通过ezone流水线平台打包Docker镜像的安全要求，涵盖镜像构建、存储和分发的全流程，确保镜像的安全性和合规性。",
     *       "tags": {
     *         "dates": [“20250501”],
     *         "locations": ["北京小米科技园"],
     *         "organizations": [
     *           "ezone流水线平台",
     *           "Harbor"
     *         ],
     *         "persons": [“开发1”],
     *         "topics": [
     *           "Docker镜像",
     *           "镜像分发"
     *         ]
     *       }
     *     }
     */
    private String abstractInfo = "{}";

    public void tranAbstractInfo(KnowledgeBaseFileAbstractInfo knowledgeBaseFileAbstractInfo) {
        this.abstractInfo = JsonUtil.of(Optional.ofNullable(knowledgeBaseFileAbstractInfo)
                                                .orElse(new KnowledgeBaseFileAbstractInfo()));
    }

    public KnowledgeBaseFileAbstractInfo toAbstractInfo() {
        if(abstractInfo == null) {
            return new KnowledgeBaseFileAbstractInfo();
        }

        return JsonUtil.of(this.abstractInfo, KnowledgeBaseFileAbstractInfo.class);
    }


    public void tranMetadataInfo(KnowledgeBaseFileMetadata knowledgeBaseFileMetadata) {

        this.metadataInfo = JsonUtil.of(Optional.ofNullable(knowledgeBaseFileMetadata)
                                                .orElse(new KnowledgeBaseFileMetadata()));

    }

    public KnowledgeBaseFileMetadata toMetadataInfo() {
        if(metadataInfo == null) {
            return new KnowledgeBaseFileMetadata();
        }

        return JsonUtil.of(this.metadataInfo, KnowledgeBaseFileMetadata.class);
    }

}
