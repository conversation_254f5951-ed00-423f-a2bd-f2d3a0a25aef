package com.ksyun.gov.titanic.userportal.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.ksyun.gov.fd.cloud.boot.util.Pager;
import com.ksyun.gov.fd.cloud.core.context.auth.AuthContextHolder;
import com.ksyun.gov.fd.cloud.core.model.entity.BaseEntity;
import com.ksyun.gov.fd.cloud.core.web.ActionResponse;
import com.ksyun.gov.fd.cloud.redis.client.FdStrRedisClient;
import com.ksyun.gov.fd.common.biz.PageReq;
import com.ksyun.gov.fd.common.biz.PageRes;
import com.ksyun.gov.fd.common.lang.Safes;
import com.ksyun.gov.fd.common.lang.Yssert;
import com.ksyun.gov.fd.common.service.api.auth.entity.req.knowledge.BindKnowledgePermissionReq;
import com.ksyun.gov.fd.common.service.api.auth.entity.req.knowledge.ViewKnowledgePermissionCodeReq;
import com.ksyun.gov.fd.common.service.api.auth.entity.req.knowledge.ViewKnowledgePermissionUserReq;
import com.ksyun.gov.fd.common.service.api.auth.fegin.KnowledgePermissionApi;
import com.ksyun.gov.fd.common.service.api.file.entity.vo.FileInfoVo;
import com.ksyun.gov.fd.common.service.api.file.fegin.FileServiceAPI;
import com.ksyun.gov.fd.common.service.api.user.entity.dto.group.dept.Dept;
import com.ksyun.gov.fd.common.service.api.user.fegin.DeptMgtAPI;
import com.ksyun.gov.fd.common.web.exception.BaseException;
import com.ksyun.gov.fd.common.web.exception.BaseExceptionEnum;
import com.ksyun.gov.fd.common.web.exception.BaseRunTimeException;
import com.ksyun.gov.titanic.userportal.biz.process.kb.ThirdPartyKnowledgeContext;
import com.ksyun.gov.titanic.userportal.biz.process.kb.ThirdPartyKnowledgeServiceFactory;
import com.ksyun.gov.titanic.userportal.biz.process.kb.ThirdPartyKnowledgeServiceWarp;
import com.ksyun.gov.titanic.userportal.common.biz.IdUtils;
import com.ksyun.gov.titanic.userportal.common.biz.UserHandler;
import com.ksyun.gov.titanic.userportal.common.enums.KbCateEnum;
import com.ksyun.gov.titanic.userportal.common.enums.KbLlmFilesStatusEnum;
import com.ksyun.gov.titanic.userportal.common.enums.KbmConstants;
import com.ksyun.gov.titanic.userportal.common.enums.LlmEnum;
import com.ksyun.gov.titanic.userportal.config.aichat.AIClientConfig;
import com.ksyun.gov.titanic.userportal.config.rag.RAGConfig;
import com.ksyun.gov.titanic.userportal.dataconvert.MetadataConvert;
import com.ksyun.gov.titanic.userportal.model.entity.*;
import com.ksyun.gov.titanic.userportal.model.param.*;
import com.ksyun.gov.titanic.userportal.model.vo.*;
import com.ksyun.gov.titanic.userportal.service.*;
import com.ksyun.gov.titanic.userportal.utils.FileSizeConverter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;

import java.io.File;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @author: xiaoshicheng
 * @date: 2024/1/26 10:35
 **/
@Component
@Slf4j
public class KnowledgeBaseManagementBiz {

    private static final Logger LOGGER = LoggerFactory.getLogger(KnowledgeBaseManagementBiz.class);

    private final KnowledgeBaseManagementService knowledgeBaseService;

    private final UserHandler userHandler;

    @Autowired
    private AiChatService aiChatService;

    public static final String FILE = "file";

    public static final String PATH = "path";

    private static final String PROJECT_PATH = System.getProperty("user.dir") + File.separator + "temp-file" + File.separator;

    private static final String IMAGE_PATH = System.getProperty("user.dir") + File.separator + "temp-images" + File.separator;

    @Autowired
    private ThirdPartyKnowledgeServiceFactory thirdPartyKnowledgeServiceFactory;

    @Autowired
    private AIClientConfig aiClientConfig;

    @Autowired
    private KnowledgeBaseFileService knowledgeBaseFileService;

    @Autowired
    private FileServiceAPI fileServiceAPI;

    @Autowired
    KnowledgeBaseFileBiz knowledgeBaseFileBiz;

    @Autowired
    private BotDefinitionService botDefinitionService;

    @Autowired
    private LlmConfigBiz LlmConfiBiz;

    @Autowired
    private KbLlmFilesService kbLlmFilesService;

    @Autowired
    private KbLlmFilesBiz kbLlmFilesBiz;

    @Autowired
    private KbsLlmsService kbsLlmsService;

    @Autowired
    private KbsLlmsBiz kbsLlmsBiz;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private FdStrRedisClient fdStrRedisClient;

    @Autowired
    private RAGConfig ragConfig;

    @Autowired
    private ChatFileBiz chatFileBiz;

    @Autowired
    private KnowledgeBaseFileZYService fileService;

    @Autowired
    private DeptMgtAPI deptMgtAPI;

    @Autowired
    private KnowledgePermissionApi knowledgePermissionApi;

    private final RestTemplate restTemplate = new RestTemplate();
    @Value("${dify.abstract_flow.url:http://10.69.81.119:8600}")
    private String difyApiUrl;
    @Value("${dify.abstract_flow.key:app-nunFRS3YBDbweJyjEMt2tWX4}")
    private String difyApiKey;

    @Value("${fd.auth.client.keycloak.realm}")
    private String realm;


    public KnowledgeBaseManagementBiz (KnowledgeBaseManagementService knowledgeBaseService, UserHandler userHandler) {
        this.knowledgeBaseService = knowledgeBaseService;
        this.userHandler = userHandler;
    }


    /**
     * @param id 要删除的知识库记录id
     * @description 根据id删除知识库记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeKnowledgeBase (Long id) {
        KnowledgeBaseManagementEntity one = knowledgeBaseService.getById(id);
        if (Objects.isNull(one)) {
            LOGGER.info("-- The record corresponding to the {} does not exis --", id);
            return;
        }

        removeKbm(one);
    }


    @Transactional(rollbackFor = Exception.class)
    public void removeKnowledgeBase (String kbmCode) {
        KnowledgeBaseManagementEntity one = knowledgeBaseService.getByCode(kbmCode, KnowledgeBaseManagementEntity::getKbmCode);
        if (Objects.isNull(one)) {
            LOGGER.info("-- The record corresponding to the {} does not exis --", kbmCode);
            return;
        }

        removeKbm(one);
    }


    private void removeKbm (KnowledgeBaseManagementEntity one) {
        List<BotDefinitionEntity> bots = botDefinitionService.getBotsByKbmCode(one.getKbmCode());
        if (CollectionUtils.isNotEmpty(bots)) {
            List<String> botNames = bots.stream().map(BotDefinitionEntity::getBotName).toList();
            throw new BaseRunTimeException(BaseExceptionEnum.SYS_ERROR.getCode(), "该知识库已被助手引用, 引用助手列表为: " + botNames);
        }

        List<KnowledgeBaseFileEntity> kbmFiles = knowledgeBaseFileService.getByKbmCode(one.getKbmCode());
        Yssert.isTrue(CollectionUtils.isEmpty(kbmFiles), "该知识库尚存在未删除的文件，请清空知识库文件后重试。");

        //  逻辑删除
        knowledgeBaseService.update(new LambdaUpdateWrapper<KnowledgeBaseManagementEntity>().eq(KnowledgeBaseManagementEntity::getId, one.getId()).set(KnowledgeBaseManagementEntity::getIsDeleted, -1));

        List<KbsLlmsEntity> kbsLlmsEntities = kbsLlmsService.listByKbmCode(one.getKbmCode());
        for (KbsLlmsEntity kbmCodeAndLlm : kbsLlmsEntities) {
            if (!Objects.isNull(kbmCodeAndLlm)) {
                LlmConfigVo lLmConfigVo = aiClientConfig.getLLmConfigVo(kbmCodeAndLlm.getLlmCode());
                Yssert.notNull(lLmConfigVo, "未找到此模型");

                ThirdPartyKnowledgeContext thirdPartyKnowledgeContext = new ThirdPartyKnowledgeContext();
                thirdPartyKnowledgeContext.setThirdPartyKbId(kbmCodeAndLlm.getLlmKbCode());
                thirdPartyKnowledgeContext.setLlmCode(kbmCodeAndLlm.getLlmCode());
                thirdPartyKnowledgeServiceFactory.get(lLmConfigVo.getClientKey()).deleteThirdPartyKbById(thirdPartyKnowledgeContext);

                kbsLlmsService.removeById(kbmCodeAndLlm.getId());
            }
        }
    }


    /**
     * @param id 要获取的记录id
     * @return KnowledgeBaseManagementEntity 知识库记录信息
     * @description 根据id获取知识库记录
     */
    public KnowledgeBaseManagementEntity getKnowledgeBase (Long id) {
        return knowledgeBaseService.getById(id);
    }

    public KnowledgeBaseVo getKnowledgeBaseV2 (Long id) {
        KnowledgeBaseManagementEntity knowledgeBaseManagementEntity = knowledgeBaseService.getById(id);
        KnowledgeBaseVo knowledgeBaseVo = new KnowledgeBaseVo();
        knowledgeBaseVo.setId(knowledgeBaseManagementEntity.getId());
        knowledgeBaseVo.setType(knowledgeBaseManagementEntity.getType());
        knowledgeBaseVo.setName(knowledgeBaseManagementEntity.getName());
        knowledgeBaseVo.setDescription(knowledgeBaseManagementEntity.getDescription());
        knowledgeBaseVo.setClassification(knowledgeBaseManagementEntity.getClassification());
        knowledgeBaseVo.setStatus(knowledgeBaseManagementEntity.getStatus());
        knowledgeBaseVo.setDeptId(knowledgeBaseManagementEntity.getDeptId());
        knowledgeBaseVo.setKbmCode(knowledgeBaseManagementEntity.getKbmCode());
        knowledgeBaseVo.setCode(knowledgeBaseManagementEntity.getKbmCode());
        knowledgeBaseVo.setCreatedTime(knowledgeBaseManagementEntity.getCreatedTime());
        knowledgeBaseVo.setFilesize("");
        knowledgeBaseVo.setFiletotal("");
        knowledgeBaseVo.setDefinitionTotal(0);
        knowledgeBaseVo.setCategory(knowledgeBaseManagementEntity.getCategory());
        knowledgeBaseVo.setOtherPlatformCode("");
        if (knowledgeBaseManagementEntity.getCategory().equals("external")) {
            Dept deptDetailDTO = null;
            try {
                deptDetailDTO = deptMgtAPI.deptDetail(knowledgeBaseManagementEntity.getDeptId(), realm).getSuccessData();
            } catch (Exception e) {
                deptDetailDTO = new Dept();
            }
            knowledgeBaseVo.setDept(deptDetailDTO);
            LambdaQueryWrapper<BotDefinitionEntity> definitionEntityLambdaQueryWrapper = new LambdaQueryWrapper<BotDefinitionEntity>()
                    .like(BotDefinitionEntity::getKbmCode, knowledgeBaseManagementEntity.getKbmCode())
                    .eq(BotDefinitionEntity::getIsDeleted, 0);
            knowledgeBaseVo.setDefinitionTotal(botDefinitionService.count(definitionEntityLambdaQueryWrapper));

            // 补充当前权限的用户列表
            ViewKnowledgePermissionUserReq viewKnowledgePermissionUserReq = new ViewKnowledgePermissionUserReq();
            viewKnowledgePermissionUserReq.setKnowledgeId(knowledgeBaseManagementEntity.getKbmCode());
            viewKnowledgePermissionUserReq.setOperateUId(AuthContextHolder.getLoginUser().getUid());
            List<String> userPermissionList = knowledgePermissionApi.ViewKnowledgePermission(viewKnowledgePermissionUserReq).getSuccessData();
            knowledgeBaseVo.setUserPermissionList(userPermissionList);
        }
        return knowledgeBaseVo;
    }


    /**
     * @param knowledgeBaseParam 知识库保存信息
     * @description 保存知识库记录
     */
    @Transactional(rollbackFor = Throwable.class)
    public String saveKnowledgeBase (KnowledgeBaseParam knowledgeBaseParam, String uid) {
        // 知识库编码
        String kbmCode = IdUtils.generateId();

        // 知识库类别
        String category = knowledgeBaseParam.getCategory();
        if (StringUtils.isNotBlank(category) && (category.equals(KbCateEnum.PERSONAL_KB.getCode()) || category.equals(KbCateEnum.TEMP_KB.getCode()))) {
            Yssert.isTrue(kbsLlmsBiz.knowledgeSync(LlmEnum.RETRIEVE_LLM_CODE.getCode(), kbmCode, false), "知识库创建失败，请联系管理员~");
        }

        // 校验知识库名称
        Yssert.isFalse(StringUtils.isBlank(knowledgeBaseParam.getName()), "知识库名称不能为空");
        if (Objects.nonNull(this.getKnowledgeBaseByName(knowledgeBaseParam.getName()))) {
            Yssert.throwEx("知识库" + knowledgeBaseParam.getName() + "已存在");
        }

        // 3创建知识库
        KnowledgeBaseManagementEntity knowledgeBaseManagementEntity = new KnowledgeBaseManagementEntity();
        BeanUtils.copyProperties(knowledgeBaseParam, knowledgeBaseManagementEntity);
        knowledgeBaseManagementEntity.setKbmCode(kbmCode);
        userHandler.entityProcess(knowledgeBaseManagementEntity);
        knowledgeBaseService.save(knowledgeBaseManagementEntity);

        // 绑定用户权限
        BindKnowledgePermissionReq bindKnowledgePermissionReq = new BindKnowledgePermissionReq();
        bindKnowledgePermissionReq.setKnowledgeId(kbmCode);
        bindKnowledgePermissionReq.setUserIds(knowledgeBaseParam.getUserIds());
        bindKnowledgePermissionReq.setOperateUId(uid);
        knowledgePermissionApi.BindKnowledgePermission(bindKnowledgePermissionReq);

        return kbmCode;
    }


    public PageRes<KnowledgeBaseVo> listKnowledgeBaseVos (KnowledgeBasePageParam knowledgeBasePageParam) {
        PageRes<KnowledgeBaseVo> resultPage = new PageRes<>();
        // 获取知识库类别
        String category = StringUtils.isBlank(knowledgeBasePageParam.getCategory()) ? KbCateEnum.EXTERNAL_KB.getCode() : knowledgeBasePageParam.getCategory();
        LambdaQueryWrapper<KnowledgeBaseManagementEntity> queryWrapper = new LambdaQueryWrapper<>();
        UserHandler.addTenantQueryColumn(queryWrapper);
        // 查询语句
        queryWrapper.like(StringUtils.isNotBlank(knowledgeBasePageParam.getName()), KnowledgeBaseManagementEntity::getName, knowledgeBasePageParam.getName())
                .eq(KnowledgeBaseManagementEntity::getCategory, category)
                .eq(KnowledgeBaseManagementEntity::getCategory, StringUtils.isBlank(knowledgeBasePageParam.getCategory()) ? KbCateEnum.EXTERNAL_KB.getCode() : knowledgeBasePageParam.getCategory())
                .eq(StringUtils.isNotBlank(knowledgeBasePageParam.getType()), KnowledgeBaseManagementEntity::getType, knowledgeBasePageParam.getType())
                .eq(Objects.nonNull(knowledgeBasePageParam.getStatus()), KnowledgeBaseManagementEntity::getStatus, knowledgeBasePageParam.getStatus())
                .in(CollectionUtils.isNotEmpty(knowledgeBasePageParam.getKbmCodes()), KnowledgeBaseManagementEntity::getKbmCode, knowledgeBasePageParam.getKbmCodes())
                .eq(KnowledgeBaseManagementEntity::getIsDeleted, 0)
                .orderBy(Boolean.TRUE, Boolean.FALSE, KnowledgeBaseManagementEntity::getCreatedTime);

        // 增加权限功能(只针对企业知识库)
        if (category.equals(KbCateEnum.EXTERNAL_KB.getCode())){
            if (!AuthContextHolder.getLoginUser().isSuperAdmin() && !AuthContextHolder.getLoginUser().isTenantAdmin()){
                ViewKnowledgePermissionCodeReq viewKnowledgePermissionCodeReq = new ViewKnowledgePermissionCodeReq();
                viewKnowledgePermissionCodeReq.setOperateUId(AuthContextHolder.getLoginUser().getUid());
                // 非管理员用户进行权限验证
                List<String> permissionKnowledgeCodes = knowledgePermissionApi.ViewKnowledgePermissionCode(viewKnowledgePermissionCodeReq).getSuccessData();
                queryWrapper.in(KnowledgeBaseManagementEntity::getKbmCode,permissionKnowledgeCodes);
            }
        }

        // 分页
        PageReq pageReq = knowledgeBasePageParam.getPageReq();
        Page<KnowledgeBaseManagementEntity> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        Page<KnowledgeBaseManagementEntity> kbmPage = knowledgeBaseService.page(page, queryWrapper);
        List<KnowledgeBaseManagementEntity> records = kbmPage.getRecords();
        List<Map<String, Object>> maps = fileService.queryTotalByKbmCode();
        List<KnowledgeBaseVo> knowledgeBaseVos = new ArrayList<KnowledgeBaseVo>();
        // 数据返回整理
        for (KnowledgeBaseManagementEntity record : records) {
            Optional<Map<String, Object>> kbmCode = maps.stream().filter(map -> Objects.equals(record.getKbmCode(), map.get("kbm_code"))).findFirst();
            KnowledgeBaseVo knowledgeBaseVo = new KnowledgeBaseVo();
            knowledgeBaseVo.setId(record.getId());
            knowledgeBaseVo.setStatus(record.getStatus());
            knowledgeBaseVo.setClassification(record.getClassification());
            knowledgeBaseVo.setCategory(record.getCategory());
            knowledgeBaseVo.setName(record.getName());
            knowledgeBaseVo.setDescription(record.getDescription());
            knowledgeBaseVo.setKbmCode(record.getKbmCode());
            knowledgeBaseVo.setCode(record.getKbmCode());
            knowledgeBaseVo.setFiletotal(kbmCode.map(m -> m.get("file_count").toString()).orElse("0"));
            knowledgeBaseVo.setFilesize(FileSizeConverter.convertFileSize(Integer.valueOf(kbmCode.map(m -> m.get("total_file_size").toString()).orElse("0"))));
            // 企业知识库设置助手与配置部门关联信息
            if (Objects.isNull(knowledgeBasePageParam.getCategory()) || knowledgeBasePageParam.getCategory().equals("external")) {
                // 获取部门信息
                Dept deptDetailDTO = null;
                try {
                    deptDetailDTO = deptMgtAPI.deptDetail(record.getDeptId(), realm).getSuccessData();
                } catch (Exception e) {
                    deptDetailDTO = new Dept();
                }
                // 获取助手数量
                knowledgeBaseVo.setDept(deptDetailDTO);
                LambdaQueryWrapper<BotDefinitionEntity> definitionEntityLambdaQueryWrapper = new LambdaQueryWrapper<BotDefinitionEntity>()
                        .like(BotDefinitionEntity::getKbmCode, record.getKbmCode())
                        .eq(BotDefinitionEntity::getIsDeleted, 0);
                knowledgeBaseVo.setDefinitionTotal(botDefinitionService.count(definitionEntityLambdaQueryWrapper));

                // 补充当前权限的用户列表
                ViewKnowledgePermissionUserReq viewKnowledgePermissionUserReq = new ViewKnowledgePermissionUserReq();
                viewKnowledgePermissionUserReq.setKnowledgeId(record.getKbmCode());
                viewKnowledgePermissionUserReq.setOperateUId(AuthContextHolder.getLoginUser().getUid());
                List<String> userPermissionList = knowledgePermissionApi.ViewKnowledgePermission(viewKnowledgePermissionUserReq).getSuccessData();
                knowledgeBaseVo.setUserPermissionList(userPermissionList);

            }
            knowledgeBaseVo.setCreatedTime(record.getCreatedTime());
            knowledgeBaseVos.add(knowledgeBaseVo);
        }
        resultPage.setCurrent(pageReq.getPageNum());
        resultPage.setSize(pageReq.getPageSize());
        resultPage.setTotal(kbmPage.getTotal());
        resultPage.setRecords(knowledgeBaseVos);
        return resultPage;
    }

    public List<Map<String,String>> listAllKnowledge (KnowledgeBaseAllParam  knowledgeBaseAllParam) {
        List<Map<String,String>> knowledgeBaseVoList = new ArrayList<>();
        // 返回所有企业知识库列表
        LambdaQueryWrapper<KnowledgeBaseManagementEntity> queryWrapper = new LambdaQueryWrapper<>();
        UserHandler.addTenantQueryColumn(queryWrapper);
        queryWrapper.eq(KnowledgeBaseManagementEntity::getCategory, knowledgeBaseAllParam.getCategory())
                .eq(KnowledgeBaseManagementEntity::getIsDeleted, 0)
                .orderBy(Boolean.TRUE, Boolean.FALSE, KnowledgeBaseManagementEntity::getCreatedTime);
        List<KnowledgeBaseManagementEntity> knowledgeBaseManagementEntityList =  knowledgeBaseService.list(queryWrapper);
        for (KnowledgeBaseManagementEntity knowledgeBaseManagementEntity :knowledgeBaseManagementEntityList){
            Map<String,String> map = new HashMap<>();
            map.put("kbmcode",knowledgeBaseManagementEntity.getKbmCode());
            map.put("name",knowledgeBaseManagementEntity.getName());
            knowledgeBaseVoList.add(map);
        }
        return knowledgeBaseVoList;
    }



    public KnowledgeBaseVo getKnowledgeBaseNameByCodeV2 (String code) {
        if (StringUtils.isBlank(code)) {
            LOGGER.error("code is blank");
            throw new BaseException(BaseExceptionEnum.SYS_ERROR);
        }
        LambdaQueryWrapper<KnowledgeBaseManagementEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBaseManagementEntity::getKbmCode, code);
        queryWrapper.eq(KnowledgeBaseManagementEntity::getIsDeleted, 0);
        KnowledgeBaseManagementEntity one = knowledgeBaseService.getOne(queryWrapper);
        if (Objects.isNull(one)) {
            LOGGER.error("-- code {} not exist --", code);
            return null;
        }
        KnowledgeBaseVo knowledgeBaseVo = new KnowledgeBaseVo();
        knowledgeBaseVo.setId(one.getId());
        knowledgeBaseVo.setType(one.getType());
        knowledgeBaseVo.setName(one.getName());
        knowledgeBaseVo.setDescription(one.getDescription());
        knowledgeBaseVo.setClassification(one.getClassification());
        knowledgeBaseVo.setStatus(one.getStatus());
        knowledgeBaseVo.setDeptId(one.getDeptId());
        knowledgeBaseVo.setKbmCode(one.getKbmCode());
        knowledgeBaseVo.setCode(one.getKbmCode());
        knowledgeBaseVo.setCreatedTime(one.getCreatedTime());
        knowledgeBaseVo.setFilesize("");
        knowledgeBaseVo.setFiletotal("");
        knowledgeBaseVo.setDefinitionTotal(0);
        knowledgeBaseVo.setCategory(one.getCategory());
        knowledgeBaseVo.setOtherPlatformCode("");
        if (one.getCategory().equals("external")) {
            Dept deptDetailDTO = null;
            try {
                deptDetailDTO = deptMgtAPI.deptDetail(one.getDeptId(), realm).getSuccessData();
            } catch (Exception e) {
                deptDetailDTO = new Dept();
            }
            knowledgeBaseVo.setDept(deptDetailDTO);
            LambdaQueryWrapper<BotDefinitionEntity> definitionEntityLambdaQueryWrapper = new LambdaQueryWrapper<BotDefinitionEntity>()
                    .like(BotDefinitionEntity::getKbmCode, one.getKbmCode())
                    .eq(BotDefinitionEntity::getIsDeleted, 0);
            knowledgeBaseVo.setDefinitionTotal(botDefinitionService.count(definitionEntityLambdaQueryWrapper));

            // 补充当前权限的用户列表
            ViewKnowledgePermissionUserReq viewKnowledgePermissionUserReq = new ViewKnowledgePermissionUserReq();
            viewKnowledgePermissionUserReq.setKnowledgeId(one.getKbmCode());
            viewKnowledgePermissionUserReq.setOperateUId(AuthContextHolder.getLoginUser().getUid());
            List<String> userPermissionList = knowledgePermissionApi.ViewKnowledgePermission(viewKnowledgePermissionUserReq).getSuccessData();
            knowledgeBaseVo.setUserPermissionList(userPermissionList);
        }
        return knowledgeBaseVo;

    }


    public KnowledgeBaseVo getKnowledgeBaseNameByCode (String code) {
        if (StringUtils.isBlank(code)) {
            LOGGER.error("code is blank");
            throw new BaseException(BaseExceptionEnum.SYS_ERROR);
        }
        LambdaQueryWrapper<KnowledgeBaseManagementEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBaseManagementEntity::getKbmCode, code);
        queryWrapper.eq(KnowledgeBaseManagementEntity::getIsDeleted, 0);
        // userHandler.queryWrapperProcess(queryWrapper, KnowledgeBaseManagementEntity::getCreatedBy);
        KnowledgeBaseManagementEntity one = knowledgeBaseService.getOne(queryWrapper);
        if (Objects.isNull(one)) {
            LOGGER.error("-- code {} not exist --", code);
            return null;
        }
        return MetadataConvert.INSTANCE.knowledgeBaseEntityToVo(one);
    }


    public void updateKnowledgeBase (Long id, KnowledgeBaseParam knowledgeBaseParam, String uid) {
        Yssert.notNull(id, "-- knowledge management id is null! --");

        KnowledgeBaseVo vo = this.getKnowledgeBaseByName(knowledgeBaseParam.getName());
        if (Objects.nonNull(vo) && !vo.getId().equals(id)) {
            Yssert.throwEx("知识库'" + knowledgeBaseParam.getName() + "'已存在");
        }

        KnowledgeBaseManagementEntity knowledgeBaseManagementEntity = MetadataConvert.INSTANCE.knowledgeBaseParamToEntity(knowledgeBaseParam);
        knowledgeBaseManagementEntity.setId(id);
        userHandler.entityUpdateProcess(knowledgeBaseManagementEntity);
        if (!knowledgeBaseService.updateById(knowledgeBaseManagementEntity)) {
            LOGGER.info("-- knowledge management id: {}, not update content --", id);
        }


        // 绑定用户权限
        BindKnowledgePermissionReq bindKnowledgePermissionReq = new BindKnowledgePermissionReq();
        bindKnowledgePermissionReq.setKnowledgeId(knowledgeBaseManagementEntity.getKbmCode());
        bindKnowledgePermissionReq.setUserIds(knowledgeBaseParam.getUserIds());
        bindKnowledgePermissionReq.setOperateUId(uid);
        knowledgePermissionApi.BindKnowledgePermission(bindKnowledgePermissionReq);


    }


    /**
     * 创建临时知识库
     *
     * @return 临时知识库id
     */
    public String createTempKbm (String llmCode) {

        return createTempChatKbm(llmCode, null);
        // 同步至模型
        // kbsLlmsBiz.knowledgeSyncLLm(llmCode, true);

        // LlmConfigVo llmConfigById = LlmConfiBiz.getLlmConfigById(llmCode);
        // String kbmCode1 = Optional.ofNullable(llmConfigById).map(LlmConfigVo::getLlmOption)
        //                          .map(LlmOption::getExpandConfigs)
        //                          .map(stringStringMap -> stringStringMap.get("kbmCode")).orElse("");
        // return kbmCode1;


        // LlmConfigVo lLmConfigVo = AIClientConfig.getLLmConfigVo(llmCode);
        // Yssert.notNull(lLmConfigVo, "未找到此模型");
        // return thirdPartyKnowledgeServiceFactory.get(lLmConfigVo.getClientKey()).createThirdPartyKb(IdUtils.generateId(), true);
    }


    public KnowledgeBaseVo getKnowledgeBaseByName (String name) {
        LambdaQueryWrapper<KnowledgeBaseManagementEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBaseManagementEntity::getName, name);
        queryWrapper.eq(KnowledgeBaseManagementEntity::getIsDeleted, 0);
        UserHandler.addTenantQueryColumn(queryWrapper);

        KnowledgeBaseManagementEntity entity = knowledgeBaseService.getOne(queryWrapper);

        if (Objects.isNull(entity)) {
            return null;
        }

        KnowledgeBaseVo vo = new KnowledgeBaseVo();
        BeanUtils.copyProperties(entity, vo);

        return vo;
    }


    /**
     * 调用Dify Flow并获取JSON内容
     *
     * @param fileCode 文件code
     * @return 解析出的text的json内容，失败返回null
     */
    private Object callDifyFlowAndGetJson (String fileCode) {
        Map<String, Object> requestBody = new HashMap<>();
        Map<String, Object> inputs = new HashMap<>();
        Map<String, Object> docFile = new HashMap<>();
        docFile.put("type", "document");
        docFile.put("transfer_method", "remote_url");
        docFile.put("url", "http://kdmp.ksyun.com/fd-file-service/file/download?code=" + fileCode);
        inputs.put("doc_file", docFile);
        requestBody.put("inputs", inputs);
        requestBody.put("response_mode", "blocking");
        requestBody.put("user", "abc-123");

        LOGGER.info("Sending request to Dify API: {}", com.ksyun.gov.fd.common.json.JsonUtil.toJson(requestBody));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        if (difyApiKey != null && !difyApiKey.isEmpty()) {
            headers.set("Authorization", "Bearer " + difyApiKey);
        }

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

        try {
            String url = difyApiUrl + "/v1/workflows/run";
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
            LOGGER.info("Received response from Dify API: {}", com.ksyun.gov.fd.common.json.JsonUtil.toJson(response.getBody()));

            Map<String, Object> data = (Map<String, Object>) response.getBody().get("data");
            if (data != null) {
                Map<String, Object> outputs = (Map<String, Object>) data.get("outputs");
                if (outputs != null && outputs.containsKey("text")) {
                    String textContent = (String) outputs.get("text");
                    try {
                        Object jsonContent = com.ksyun.gov.fd.common.json.JsonUtil.of(textContent, Object.class);
                        return jsonContent;
                    } catch (Exception ex) {
                        LOGGER.warn("Failed to parse text content as JSON: {}", ex.getMessage());
                        return null;
                    }
                }
            }
            return null;
        } catch (Exception e) {
            LOGGER.error("Error calling Dify API", e);
            return null;
        }
    }


    public List<String> saveKnowledgeBaseFileInfo (String kbmCode, List<KnowledgeBaseFileParam> params) {
        String uid = UserHandler.getUserId();
        List<KnowledgeBaseFileEntity> entities = params.stream().map(e -> {
            KnowledgeBaseFileEntity entity = new KnowledgeBaseFileEntity();
            BeanUtils.copyProperties(e, entity);
            entity.setFileCode(IdUtils.generateId());
            entity.setCreatedBy(uid);
            entity.setUpdatedBy(uid);
            entity.setKbmCode(kbmCode);
            entity.tranMetadataInfo(e.getMetadataInfo());
            // 新增：调用flow获取摘要
            Object abstractInfo = callDifyFlowAndGetJson(entity.getOssFileCode());
            if (abstractInfo != null) {
                entity.setAbstractInfo(com.ksyun.gov.fd.common.json.JsonUtil.toJson(abstractInfo));
            }
            return entity;
        }).collect(Collectors.toList());

        List<String> llmCodes = kbsLlmsService.getLlmCodeByKbmCode(kbmCode);

        Map<String, FileInfoVo> map = kbLlmFilesBiz.oosFileMap(Safes.of(entities).stream().filter(x -> x.getFileName().length() <= 100).map(KnowledgeBaseFileEntity::getOssFileCode).distinct().collect(Collectors.toList()));
        List<String> errorFiles = new ArrayList<>();
        for (KnowledgeBaseFileEntity entity : entities) {
            String ossFileCode = entity.getOssFileCode();
            // 校验文件名称是否超出长度
            if (entity.getFileName().length() > 50) {
                errorFiles.add(ossFileCode);
                continue;
            }
            // 校验知识库下未删除的文件名是否重复
            KnowledgeBaseFileEntity one = knowledgeBaseFileService.getOne(new LambdaQueryWrapper<KnowledgeBaseFileEntity>()
                    .eq(KnowledgeBaseFileEntity::getKbmCode, kbmCode)
                    .eq(KnowledgeBaseFileEntity::getFileName, entity.getFileName())
                    .eq(KnowledgeBaseFileEntity::getStatus, 1));

            if (Objects.isNull(one)) {
                entity.setMd5(map.get(ossFileCode).getMd5());
                knowledgeBaseFileService.save(entity);
                if (CollectionUtils.isNotEmpty(llmCodes))
                    kbLlmFilesBiz.addThirdPartyKbFile(kbmCode, llmCodes, entity.getFileCode(), ossFileCode, map.get(ossFileCode).getOriginalName());
            } else errorFiles.add(ossFileCode);
        }

        return errorFiles;
    }


    /**
     * 知识库文件保存
     *
     * @param kbmCode
     * @param params
     * @return
     */
    public List<KBFileUploadFailVo> saveKnowledgeBaseFileInfoV2 (String kbmCode, List<KnowledgeBaseFileParam> params) {
        List<KBFileUploadFailVo> errorFiles = new ArrayList<>();
        List<KnowledgeBaseFileEntity> entities = params.stream().map(e -> {
            // 校验文件名称是否超出长度
            if (e.getFileName().length() > 50) {
                KBFileUploadFailVo failVo = buildFileUploadFailVo(e, "文件名称超长，文件名长度限制50");
                errorFiles.add(failVo);
                return null;
            }
            // 校验知识库下未删除的文件名是否重复
            KnowledgeBaseFileEntity one = knowledgeBaseFileService.getOne(new LambdaQueryWrapper<KnowledgeBaseFileEntity>()
                    .eq(KnowledgeBaseFileEntity::getKbmCode, kbmCode)
                    .eq(KnowledgeBaseFileEntity::getFileName, e.getFileName())
                    .eq(KnowledgeBaseFileEntity::getStatus, 1));
            if (Objects.nonNull(one)) {
                KBFileUploadFailVo failVo = buildFileUploadFailVo(e, "文件名称重复");
                errorFiles.add(failVo);
                return null;
            }

            String uid = UserHandler.getUserId();
            KnowledgeBaseFileEntity entity = new KnowledgeBaseFileEntity();
            BeanUtils.copyProperties(e, entity);
            entity.setFileCode(IdUtils.generateId());
            entity.setCreatedBy(uid);
            entity.setUpdatedBy(uid);
            entity.setKbmCode(kbmCode);
            entity.tranMetadataInfo(e.getMetadataInfo());
            // 新增：调用flow获取摘要
            Object abstractInfo = callDifyFlowAndGetJson(entity.getOssFileCode());
            if (abstractInfo != null) {
                entity.setAbstractInfo(com.ksyun.gov.fd.common.json.JsonUtil.toJson(abstractInfo));
            }
            return entity;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        // 校验是否有符合条件的文件
        if (CollUtil.isEmpty(entities)) return errorFiles;

        List<String> llmCodes = kbsLlmsService.getLlmCodeByKbmCode(kbmCode);
        Map<String, FileInfoVo> map = kbLlmFilesBiz.oosFileMap(Safes.of(entities).stream().filter(x -> x.getFileName().length() <= 100).map(KnowledgeBaseFileEntity::getOssFileCode).distinct().collect(Collectors.toList()));
        for (KnowledgeBaseFileEntity entity : entities) {
            String ossFileCode = entity.getOssFileCode();
            entity.setMd5(map.get(ossFileCode).getMd5());
            knowledgeBaseFileService.save(entity);
            if (CollectionUtils.isNotEmpty(llmCodes))
                kbLlmFilesBiz.addThirdPartyKbFile(kbmCode, llmCodes, entity.getFileCode(), ossFileCode, map.get(ossFileCode).getOriginalName());
        }

        return errorFiles;

    }


    @NotNull
    private KBFileUploadFailVo buildFileUploadFailVo (KnowledgeBaseFileParam param, String msg) {
        KBFileUploadFailVo failVo = new KBFileUploadFailVo();
        BeanUtils.copyProperties(param, failVo);
        failVo.setMsg(msg);
        return failVo;
    }


    public PageRes<KnowledgeBaseFileVo> pageKnowledgeBaseFileList (KnowledgeBaseFileQueryParam req) {
        Yssert.notEmpty(req.getKbmCode(), "知识库编码不能为空");

        // 文件同步状态筛选
        if (Objects.nonNull(req.getSyncStatus()) && StringUtils.isNotBlank(req.getKbmCode())) {
            String kbmCode = req.getKbmCode();
            Integer synStatus = req.getSyncStatus();

            Map<String, Integer> fileStatusMap = Safes.of(kbLlmFilesService.listByKbmCode(kbmCode))
                    .stream()
                    .collect(Collectors.toMap(KbLlmFilesEntity::getFileCode, BaseEntity::getStatus, (o1, o2) -> {
                        if (o1.equals(KbLlmFilesStatusEnum.FAIL.getCode()) || o2.equals(KbLlmFilesStatusEnum.FAIL.getCode())) {
                            return KbLlmFilesStatusEnum.FAIL.getCode();
                        }
                        if (o1.equals(KbLlmFilesStatusEnum.RUNNING.getCode()) || o2.equals(KbLlmFilesStatusEnum.RUNNING.getCode())) {
                            return KbLlmFilesStatusEnum.RUNNING.getCode();
                        }

                        if (o1.equals(KbLlmFilesStatusEnum.INIT.getCode()) || o2.equals(KbLlmFilesStatusEnum.INIT.getCode())) {
                            // return KbLlmFilesStatusEnum.INIT.getCode();
                            return KbLlmFilesStatusEnum.FAIL.getCode();
                        }

                        return KbLlmFilesStatusEnum.SUCCESS.getCode();
                    }));

            List<String> fileCodes = fileStatusMap.entrySet().stream()
                    .filter(entry -> {
                        return Objects.equals(entry.getValue(), synStatus);
                    })
                    .filter(Objects::nonNull)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            if (req.getSyncStatus().equals(KbLlmFilesStatusEnum.INIT.getCode())) {
                if (CollectionUtils.isEmpty(fileStatusMap.keySet())) {
                    fileCodes = Lists.newArrayList();
                } else {
                    List<String> initFileCodes = Safes.of(knowledgeBaseFileService.list(new LambdaQueryWrapper<KnowledgeBaseFileEntity>()
                                    .eq(KnowledgeBaseFileEntity::getKbmCode, req.getKbmCode())
                                    .notIn(KnowledgeBaseFileEntity::getFileCode, fileStatusMap.keySet())
                            ))
                            .stream()
                            .map(KnowledgeBaseFileEntity::getFileCode)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(initFileCodes)) {
                        return PageRes.of(req.getPageNum(), req.getPageSize(), 0);
                    }
                    fileCodes.addAll(initFileCodes);

                }

            } else {
                if (CollectionUtils.isEmpty(fileCodes)) {
                    return PageRes.of(req.getPageNum(), req.getPageSize(), 0);
                }
            }


            List<String> strings = Safes.of(req.getFileCodes());
            strings.addAll(fileCodes);
            req.setFileCodes(strings);
        }


        LambdaQueryWrapper<KnowledgeBaseFileEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBaseFileEntity::getKbmCode, req.getKbmCode())
                .eq(KnowledgeBaseFileEntity::getStatus, 1)
                .like(org.apache.commons.lang3.StringUtils.isNotBlank(req.getKeyword()), KnowledgeBaseFileEntity::getFileName, req.getKeyword())
                .in(CollectionUtils.isNotEmpty(req.getFileCodes()), KnowledgeBaseFileEntity::getFileCode, req.getFileCodes())
                .orderByDesc(KnowledgeBaseFileEntity::getCreatedTime);

        return Pager.of(knowledgeBaseFileService.page(new Page<>(req.getPageNum(), req.getPageSize()), queryWrapper), this::getKnowledgeBaseFileVoList);
    }


    public List<KnowledgeBaseFileVo> getKnowledgeBaseFileList (String kbmCode, String fileName, String synStatus) {
        if (StringUtils.isBlank(kbmCode)) {
            throw new BaseRunTimeException(BaseExceptionEnum.SYS_ERROR.getCode(), "kbmCode is blank");
        }


        LambdaQueryWrapper<KnowledgeBaseFileEntity> queryWrapper = new LambdaQueryWrapper<>();
        UserHandler.addTenantQueryColumn(queryWrapper);
        queryWrapper.eq(KnowledgeBaseFileEntity::getKbmCode, kbmCode)
                .eq(KnowledgeBaseFileEntity::getStatus, 1)
                .like(org.apache.commons.lang3.StringUtils.isNotBlank(fileName), KnowledgeBaseFileEntity::getFileName, fileName)
                .orderByDesc(KnowledgeBaseFileEntity::getCreatedTime);


        List<KnowledgeBaseFileEntity> list = knowledgeBaseFileService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        // 文件同步状态筛选
        if (StringUtils.isNotBlank(synStatus)) {
            Map<String, Integer> fileStatusMap = Safes.of(kbLlmFilesService.listByKbmCode(kbmCode))
                    .stream()
                    .collect(Collectors.toMap(KbLlmFilesEntity::getFileCode, BaseEntity::getStatus, (o1, o2) -> {
                        if (o1.equals(KbLlmFilesStatusEnum.FAIL.getCode()) || o2.equals(KbLlmFilesStatusEnum.FAIL.getCode())) {
                            return KbLlmFilesStatusEnum.FAIL.getCode();
                        }
                        if (o1.equals(KbLlmFilesStatusEnum.RUNNING.getCode()) || o2.equals(KbLlmFilesStatusEnum.RUNNING.getCode())) {
                            return KbLlmFilesStatusEnum.RUNNING.getCode();
                        }

                        if (o1.equals(KbLlmFilesStatusEnum.INIT.getCode()) || o2.equals(KbLlmFilesStatusEnum.INIT.getCode())) {
                            return KbLlmFilesStatusEnum.INIT.getCode();
                        }

                        return KbLlmFilesStatusEnum.SUCCESS.getCode();
                    }));
            list = Safes.of(list)
                    .stream()
                    .filter(knowledgeBaseFileEntity -> Objects.equals(fileStatusMap.get(knowledgeBaseFileEntity.getFileCode()) + "", synStatus))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return getKnowledgeBaseFileVoList(list);

    }


    public List<KnowledgeBaseFileVo> getKnowledgeFileInfoByFileCode (List<String> fileCodes, String tenantId) {
        LambdaQueryWrapper<KnowledgeBaseFileEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBaseFileEntity::getTenantId, tenantId);
        queryWrapper.eq(KnowledgeBaseFileEntity::getStatus, 1);
        queryWrapper.in(KnowledgeBaseFileEntity::getFileCode, fileCodes).orderBy(Boolean.TRUE, Boolean.FALSE, KnowledgeBaseFileEntity::getCreatedTime);
        List<KnowledgeBaseFileEntity> list = knowledgeBaseFileService.list(queryWrapper);

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }


        return getKnowledgeBaseFileVoList(list);
    }


    @NotNull
    private List<KnowledgeBaseFileVo> getKnowledgeBaseFileVoList (List<KnowledgeBaseFileEntity> list) {
        List<String> ossFileCodes = list.stream().map(KnowledgeBaseFileEntity::getOssFileCode).collect(Collectors.toList());
        List<FileInfoVo> data = Lists.partition(ossFileCodes, 100).stream().flatMap(strings -> {
            ActionResponse<List<FileInfoVo>> fileItems = fileServiceAPI.getFileItems(strings);
            List<FileInfoVo> data1 = fileItems.getData();
            return data1.stream();
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(data)) {
            LOGGER.error("文件服未发现文件信息，ossFileCodes: {}", ossFileCodes);
            return Collections.emptyList();
        }
        Map<String, FileInfoVo> fileMap = data.stream().collect(Collectors.toMap(FileInfoVo::getCode, v -> v));

        List<KnowledgeBaseFileVo> knowledgeBaseFileVos = MetadataConvert.INSTANCE.listKnowledgeBaseFileEntityToVo(list);
        for (KnowledgeBaseFileVo knowledgeBaseFileVo : knowledgeBaseFileVos) {
            FileInfoVo fileInfoVo = fileMap.get(knowledgeBaseFileVo.getOssFileCode());
            knowledgeBaseFileVo.setUrl(fileInfoVo.getUrl());
            knowledgeBaseFileVo.setPreviewUrl(fileInfoVo.getPreviewUrl());
            knowledgeBaseFileVo.setExtraInfo(fileInfoVo.getExtraInfo());
            // 查看文件是否同步到第三方
            List<KbLlmFilesEntity> kbLlmFilesEntities = kbLlmFilesService.listFileSynInfo(knowledgeBaseFileVo.getFileCode(), null, null);
            if (CollectionUtils.isEmpty(kbLlmFilesEntities)) {
                knowledgeBaseFileVo.setSynStatus(KbLlmFilesStatusEnum.INIT.getName());
            } else {
                Set<KbLlmFilesEntity> failFileSet = kbLlmFilesEntities.stream().filter(e -> KbLlmFilesStatusEnum.FAIL.getCode().equals(e.getStatus())).collect(Collectors.toSet());
                Set<KbLlmFilesEntity> initFileSet = kbLlmFilesEntities.stream().filter(e -> KbLlmFilesStatusEnum.INIT.getCode().equals(e.getStatus())).collect(Collectors.toSet());
                Set<KbLlmFilesEntity> runFileSet = kbLlmFilesEntities.stream().filter(e -> KbLlmFilesStatusEnum.RUNNING.getCode().equals(e.getStatus())).collect(Collectors.toSet());
                Set<KbLlmFilesEntity> successFileSet = kbLlmFilesEntities.stream().filter(e -> KbLlmFilesStatusEnum.SUCCESS.getCode().equals(e.getStatus())).collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(failFileSet)) {
                    if (CollectionUtils.isEmpty(runFileSet)) {
                        if (successFileSet.isEmpty()) {
                            knowledgeBaseFileVo.setSynStatus(KbLlmFilesStatusEnum.INIT.getName());
                        } else {
                            knowledgeBaseFileVo.setSynStatus(KbLlmFilesStatusEnum.SUCCESS.getName());
                        }
                    } else {
                        knowledgeBaseFileVo.setSynStatus(KbLlmFilesStatusEnum.RUNNING.getName());
                    }

                } else {
                    knowledgeBaseFileVo.setSynStatus(KbLlmFilesStatusEnum.FAIL.getName());
                }
            }
        }


        return knowledgeBaseFileVos;
    }


    public void deleteKnowledgeFileByCode (String fileCode) {
        // 1、删除本地文件信息
        LambdaQueryWrapper<KnowledgeBaseFileEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBaseFileEntity::getFileCode, fileCode);
        queryWrapper.eq(KnowledgeBaseFileEntity::getStatus, 1);

        KnowledgeBaseFileEntity one = knowledgeBaseFileService.getOne(queryWrapper);
        if (Objects.isNull(one)) {
            throw new BaseRunTimeException(BaseExceptionEnum.SYS_ERROR.getCode(), String.format("fileCode: %s 对应的记录不存在", fileCode));
        }

        // 2、级联删除第三方文件和知识库文件和模型知识库文件映射关系表记录
        List<String> llmCodes = kbsLlmsService.getLlmCodeByKbmCode(one.getKbmCode());
        AtomicReference<Boolean> deleteStatus = new AtomicReference<>(Boolean.TRUE);
        if (CollectionUtils.isNotEmpty(llmCodes)) {
            for (String llmCode : llmCodes) {
                LlmConfigVo llmConfig = LlmConfiBiz.getLlmConfigById(llmCode);
                if (Objects.isNull(llmConfig)) {
                    LOGGER.info("llmCode: {} 对应的记录不存在", llmCode);
                    continue;
                }

                KbLlmFilesEntity kbLlmFilesEntity = kbLlmFilesService.getByFileCodeAndLlmCode(fileCode, llmCode, one.getKbmCode());
                if (Objects.isNull(kbLlmFilesEntity)) {
                    LOGGER.info("fileCode:{}, llmCode: {}, kbmCode:{} 对应的记录不存在", fileCode, llmCode, one.getKbmCode());
                } else {
                    transactionTemplate.executeWithoutResult(transactionStatus -> {
                        try {
                            // 3、刪除知识库文件和模型知识库文件映射关系表记录
                            kbLlmFilesService.removeById(kbLlmFilesEntity.getId());

                            // 4、级联删除第三方文件
                            if (StringUtils.isNotBlank(kbLlmFilesEntity.getLlmFileCode())) {
                                ThirdPartyKnowledgeServiceWarp thirdPartyKnowledgeServiceWarp = thirdPartyKnowledgeServiceFactory.get(llmConfig.getClientKey());
                                thirdPartyKnowledgeServiceWarp.deleteThirdPartyKbFile(kbLlmFilesEntity.getLlmKbCode(), kbLlmFilesEntity.getLlmFileCode(), kbLlmFilesEntity.getLlmCode());
                            }
                        } catch (Exception e) {
                            LOGGER.error(e.getMessage(), e);
                            deleteStatus.set(Boolean.FALSE);
                            throw new RuntimeException(e.getMessage(), e);
                        }

                    });
                }

            }
        }
        if (deleteStatus.get()) {
            // knowledgeBaseFileService.remove(queryWrapper);
            knowledgeBaseFileService.update(new LambdaUpdateWrapper<KnowledgeBaseFileEntity>().eq(KnowledgeBaseFileEntity::getFileCode, fileCode).set(KnowledgeBaseFileEntity::getStatus, 0));
        }


        try {
            chatFileBiz.delIndexChatKb(Lists.newArrayList(fileCode));
        } catch (Exception e) {
            log.error("delIndexChatKb error", e);
        }
    }


    /**
     * 删除知识库及文件
     *
     * @param kbmCode
     * @return
     */
    public Boolean deleteByKbmCode (String kbmCode) {
        if (StringUtils.isBlank(kbmCode)) return false;
        //  查询知识库
        KnowledgeBaseManagementEntity kbmEntity = knowledgeBaseService.getByKbmCode(kbmCode);
        if (Objects.isNull(kbmEntity)) {
            return false;
        }

        List<KnowledgeBaseFileEntity> kbFileEntities = knowledgeBaseFileService.getByKbmCode(kbmCode);
        kbFileEntities.forEach(x -> {
            deleteKnowledgeFileByCode(x.getFileCode());
        });

        removeKnowledgeBase(kbmCode);
        return true;
    }


    public String saveOneKnowledgeBaseFileInfo (String kbmCode, KnowledgeBaseFileParam param) {
        String uid = UserHandler.getUserId();

        KnowledgeBaseFileEntity entity = new KnowledgeBaseFileEntity();
        BeanUtils.copyProperties(param, entity);
        entity.setFileCode(IdUtils.generateId());
        entity.setCreatedBy(uid);
        entity.setUpdatedBy(uid);
        entity.setKbmCode(kbmCode);
        entity.tranMetadataInfo(param.getMetadataInfo());

        List<String> llmCodes = kbsLlmsService.getLlmCodeByKbmCode(kbmCode);
        if (CollectionUtils.isNotEmpty(llmCodes)) {
            // 校验通过一个知识库下文件名是否重复
            LambdaQueryWrapper<KnowledgeBaseFileEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(KnowledgeBaseFileEntity::getKbmCode, kbmCode).eq(KnowledgeBaseFileEntity::getFileName, entity.getFileName());
            queryWrapper.eq(KnowledgeBaseFileEntity::getStatus, 1);
            KnowledgeBaseFileEntity one = knowledgeBaseFileService.getOne(queryWrapper);
            if (!Objects.isNull(one)) {
                throw new BaseRunTimeException(BaseExceptionEnum.SYS_ERROR.getCode(), "文件名称重复");
            }
            try {
                String ossFileCode = entity.getOssFileCode();
                kbLlmFilesBiz.addThirdPartyKbFile(kbmCode, llmCodes, entity.getFileCode(), ossFileCode, fileServiceAPI.getFileItem(ossFileCode).getSuccessData().getOriginalName());
            } catch (Exception e) {
                LOGGER.error(entity.getFileName() + "同步失败", e);
                throw new BaseRunTimeException(BaseExceptionEnum.SYS_ERROR.getCode(), String.format("文件%s同步失败: %s", entity.getFileName(), e.getMessage()));
            }
        }

        entity.setMd5(Optional.ofNullable(kbLlmFilesBiz.oosFileMap(entity.getFileName().length() <= 100 ? List.of(entity.getOssFileCode()) : List.of()).get(entity.getOssFileCode())).map(FileInfoVo::getMd5).orElse(null));

        // 新增：调用flow获取摘要
        // TODO: 搞成异步的
        Object abstractInfo = callDifyFlowAndGetJson(entity.getOssFileCode());
        if (abstractInfo != null) {
            entity.setAbstractInfo(com.ksyun.gov.fd.common.json.JsonUtil.toJson(abstractInfo));
        }

        knowledgeBaseFileService.save(entity);
        return entity.getFileCode();
    }


    public String createTempChatKbm (String llmCode, String chatCode) {
        // 创建本地临时知识库

        String chatKbCode = fdStrRedisClient.get(String.format("chatkb:chat_chatKb_index_%s", chatCode));
        if (org.apache.commons.lang3.StringUtils.isNotBlank(chatKbCode)) {
            return chatKbCode;
        }


        KnowledgeBaseParam knowledgeBaseParam = new KnowledgeBaseParam();
        knowledgeBaseParam.setLlmCode(llmCode);
        knowledgeBaseParam.setName(IdUtil.fastSimpleUUID());
        knowledgeBaseParam.setCategory(KbCateEnum.TEMP_KB.getCode());
        knowledgeBaseParam.setStatus(KbmConstants.kbStatus.temp.getType());
        knowledgeBaseParam.setType("");
        String kbmCode = saveKnowledgeBase(knowledgeBaseParam, "");

        return kbmCode;

    }


    public String saveChatKnowledgeBaseFileInfo (String kbmCode, KnowledgeBaseFileParam param) {
        String uid = UserHandler.getUserId();

        KnowledgeBaseFileEntity entity = new KnowledgeBaseFileEntity();
        BeanUtils.copyProperties(param, entity);
        entity.setFileCode(IdUtils.generateId());
        entity.setCreatedBy(uid);
        entity.setUpdatedBy(uid);
        entity.setKbmCode(kbmCode);
        String ossFileCode = entity.getOssFileCode();


        LambdaQueryWrapper<KnowledgeBaseFileEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBaseFileEntity::getKbmCode, kbmCode).eq(KnowledgeBaseFileEntity::getFileName, entity.getFileName());
        queryWrapper.eq(KnowledgeBaseFileEntity::getStatus, 1);
        KnowledgeBaseFileEntity one = knowledgeBaseFileService.getOne(queryWrapper);
        // 重名覆盖
        if (Objects.nonNull(one)) {
            knowledgeBaseFileService.update(new LambdaUpdateWrapper<KnowledgeBaseFileEntity>()
                    .eq(KnowledgeBaseFileEntity::getFileCode, one.getFileCode())
                    .set(KnowledgeBaseFileEntity::getStatus, 0));
            chatFileBiz.delIndexChatKb(Lists.newArrayList(one.getFileCode()));
        }

        // 下载文件
        String fileName = fileServiceAPI.getFileItem(ossFileCode).getSuccessData().getOriginalName();
        File file = this.knowledgeBaseFileBiz.downFileByCode(ossFileCode, fileName);
        try {
            Yssert.isTrue(chatFileBiz.indexChatKb(file, kbmCode, entity), fileName + "文件索引失败");
        } finally {

            if (!Objects.isNull(file)) {
                LOGGER.info("开始删除本地临时文件,路径: {}", file.getPath());
                boolean deleteStatus = file.delete();
                LOGGER.info("本地文件删除: {}", Objects.equals(deleteStatus, Boolean.TRUE) ? "成功" : "失败");
            }
        }

        entity.setMd5(Optional.ofNullable(kbLlmFilesBiz.oosFileMap(entity.getFileName().length() <= 100 ? List.of(entity.getOssFileCode()) : List.of()).get(entity.getOssFileCode())).map(FileInfoVo::getMd5).orElse(null));
        knowledgeBaseFileService.save(entity);
        return entity.getFileCode();
    }


    public List<FileSynInfoVo> listFileSynInfo (String fileCode, String llmCode) {
        List<KbLlmFilesEntity> kbLlmFilesEntities = kbLlmFilesService.listFileSynInfo(fileCode, llmCode, Lists.newArrayList(KbLlmFilesStatusEnum.FAIL.getCode(), KbLlmFilesStatusEnum.INIT.getCode()));
        if (CollectionUtils.isEmpty(kbLlmFilesEntities)) {
            return ListUtil.empty();
        }

        List<FileSynInfoVo> fileSynInfoVos = MetadataConvert.INSTANCE.listKbLlmFilesEntityToVo(kbLlmFilesEntities);

        Set<String> llmCodes = kbLlmFilesEntities.stream().map(e -> e.getLlmCode()).collect(Collectors.toSet());
        List<LlmConfigEntity> llmConfigEntities = LlmConfiBiz.listLlmConfigByCodes(llmCodes);

        if (CollectionUtils.isEmpty(llmConfigEntities)) {
            return fileSynInfoVos;
        }
        Map<String, LlmConfigEntity> llmConfigMap = llmConfigEntities.stream().collect(Collectors.toMap(e -> e.getLlmCode(), Function.identity()));
        for (FileSynInfoVo fileSynInfoVo : fileSynInfoVos) {
            LlmConfigEntity llmConfigEntity = llmConfigMap.get(fileSynInfoVo.getLlmCode());
            if (Objects.isNull(llmConfigEntity)) {
                LOGGER.warn("llmCode:{}, 对应的模型配置为空", fileSynInfoVo.getLlmCode());
                continue;
            }
            fileSynInfoVo.setLlmName(llmConfigEntity.getLlmCode());
        }

        return fileSynInfoVos;
    }


    public void retryFileSyn (RetryFileSynParam param) {
        LambdaQueryWrapper<KnowledgeBaseFileEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBaseFileEntity::getFileCode, param.getFileCode());
        queryWrapper.eq(KnowledgeBaseFileEntity::getStatus, 1);
        KnowledgeBaseFileEntity one = knowledgeBaseFileService.getOne(queryWrapper);
        if (Objects.isNull(one)) {
            LOGGER.error("fileCode: {} 对应的文件记录信息不存在", param.getFileCode());
            throw new BaseRunTimeException(BaseExceptionEnum.SYS_ERROR.getCode(), "文件记录信息不存在");
        }
        String ossFileCode = one.getOssFileCode();
        kbLlmFilesBiz.addThirdPartyKbFile(one.getKbmCode(), param.getLlmCodes(), param.getFileCode(), ossFileCode, fileServiceAPI.getFileItem(ossFileCode).getSuccessData().getOriginalName());
    }


    public void retryKbFileSyn (List<String> kbmCodes, String llmCode) {
        for (String kbmCode : kbmCodes) {
            List<KbLlmFilesEntity> kbLlmFilesEntities = kbLlmFilesService.listByKbmCode(kbmCode);
            for (KbLlmFilesEntity kbLlmFilesEntity : kbLlmFilesEntities) {
                RetryFileSynParam param = new RetryFileSynParam();
                param.setFileCode(kbLlmFilesEntity.getFileCode());
                param.setLlmCodes(Lists.newArrayList(kbLlmFilesEntity.getLlmCode()));
                retryFileSyn(param);
            }
        }
    }


    /**
     * 查询个人知识库编码
     *
     * @param uid
     * @return
     */
    public String getPkbCode (String uid) {
        if (Objects.isNull(knowledgeBaseService.getByKbmCode(uid))) {
            KnowledgeBaseParam param = new KnowledgeBaseParam();
            param.setCategory(KbCateEnum.PERSONAL_KB.getCode());
            param.setName(uid);
            uid = saveKnowledgeBase(param, uid);
        }

        return uid;
    }


    /**
     * 查询个人知识库第三方编码
     *
     * @param uid
     * @param chatCode
     * @return
     */
    public String getPkbLlmCode (String uid, String chatCode) {
        FdStrRedisClient fdRedisClient = SpringUtil.getBean(FdStrRedisClient.class);
        String enabled = fdRedisClient.<String>get("enabledPkb" + chatCode);

        KbsLlmsEntity kbsLlmsEntity = Safes.last(kbsLlmsService.listByKbmCode(uid));
        return Boolean.parseBoolean(enabled) && Objects.nonNull(kbsLlmsEntity) ? kbsLlmsEntity.getLlmKbCode() : "";
    }


    /**
     * 附件上传
     *
     * @param chatCode
     * @return
     */
    public UploadAttVo uploadAtt (String chatCode, String chatKbCode, KnowledgeBaseFileParam param) {
        // 创建知识库
        KnowledgeBaseManagementEntity kbmEntity = null;

        if (StringUtils.isNotBlank(chatKbCode)) {
            kbmEntity = knowledgeBaseService.getByKbmCode(chatKbCode);
            Yssert.isFalse(Objects.isNull(kbmEntity), "您指定的知识库不存在，请联系管理员。chatKbCode：" + chatKbCode);
        }

        if (Objects.isNull(kbmEntity)) {
            if (StringUtils.isNotBlank(chatCode)) {
                kbmEntity = knowledgeBaseService.getByCode(chatCode, KnowledgeBaseManagementEntity::getName);
            }
            // 1、知识库编码
            chatKbCode = Optional.ofNullable(kbmEntity).map(KnowledgeBaseManagementEntity::getKbmCode).orElseGet(() -> {
                KnowledgeBaseParam knowledgeBaseParam = new KnowledgeBaseParam();
                knowledgeBaseParam.setName(StringUtils.isBlank(chatCode) ? IdUtils.generateId() : chatCode);
                knowledgeBaseParam.setCategory(KbCateEnum.TEMP_KB.getCode());
                knowledgeBaseParam.setType("");

                return saveKnowledgeBase(knowledgeBaseParam, "");
            });
        }

        // 2、文件编码
        String fileCode = saveOneKnowledgeBaseFileInfo(chatKbCode, param);

        UploadAttVo uploadAttVo = new UploadAttVo();
        uploadAttVo.setChatKbCode(chatKbCode);
        uploadAttVo.setFileCode(fileCode);
        return uploadAttVo;
    }


    /**
     * 记录删除的chatKbCode，方法结束前输出log
     * V2
     *
     * @param day
     */
    public void cleanChatKbExpiredFileV2 (Integer day) {
        // 获取当前时间
        LocalDate now = LocalDate.now();
        LocalDate expiredDay = now.minusDays(day);
        LocalDate expiredStartDay = now.minusDays(day + 5);

        List<String> chatKbNames = Safes.of(aiChatService.list(new LambdaQueryWrapper<AiChatEntity>()
                        .lt(AiChatEntity::getLastUseTime, expiredDay)
                        .ge(AiChatEntity::getLastUseTime, expiredStartDay)
                        .select(AiChatEntity::getChatCode)))
                .stream()
                .map(AiChatEntity::getChatCode)
                .toList();

        List<String> chatKbCodes = Safes.of(knowledgeBaseService.list(new LambdaQueryWrapper<KnowledgeBaseManagementEntity>().in(KnowledgeBaseManagementEntity::getName, chatKbNames))).stream().map(KnowledgeBaseManagementEntity::getKbmCode).toList();
        Map<String, List<KnowledgeBaseFileEntity>> kbmFileMap = Safes.of(knowledgeBaseFileService.getByKbmCodes(chatKbCodes)).stream().collect(Collectors.groupingBy(KnowledgeBaseFileEntity::getKbmCode));
        for (String chatKbCode : chatKbCodes) {
            try {
                List<KnowledgeBaseFileEntity> kbfEntities = kbmFileMap.get(chatKbCode);
                if (CollUtil.isEmpty(kbfEntities)) {
                    chatKbCodes.remove(chatKbCode);
                    continue;
                }

                List<String> ossFileCodes = Safes.of(kbfEntities).stream().map(KnowledgeBaseFileEntity::getOssFileCode).toList();
                if (CollUtil.isEmpty(ossFileCodes)) continue;

                // 文件服务文件删除
                fileServiceAPI.batchDel(ossFileCodes);
                // 删除知识库文件
                for (KnowledgeBaseFileEntity kbfEntity : kbfEntities) {
                    deleteKnowledgeFileByCode(kbfEntity.getFileCode());
                }

                // 删除知识库
                removeKnowledgeBase(chatKbCode);

            } catch (Exception e) {
                log.error("cleanChatKbExpiredFile foreach error", e);
            }
        }

        log.info("cleanChatKbExpiredFile chatKbCodes:{}", chatKbCodes);

    }

    /**
     * 根据 fileCode 查询摘要信息
     */
    public KnowledgeBaseFileAbstractInfo getFileAbstractInfoByFileCode (String fileCode) {
        KnowledgeBaseFileEntity entity = knowledgeBaseFileService.getOne(
                new LambdaQueryWrapper<KnowledgeBaseFileEntity>()
                        .eq(KnowledgeBaseFileEntity::getFileCode, fileCode)
                        .eq(KnowledgeBaseFileEntity::getStatus, 1)
        );
        Yssert.notNull(entity, "文件不存在");
        return entity.toAbstractInfo();
    }

}
