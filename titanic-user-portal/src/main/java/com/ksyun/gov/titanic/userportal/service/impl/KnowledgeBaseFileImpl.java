package com.ksyun.gov.titanic.userportal.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ksyun.gov.fd.cloud.boot.mybatis.BaseTenantServiceImpl;
import com.ksyun.gov.fd.common.biz.PageReq;
import com.ksyun.gov.fd.common.biz.PageRes;
import com.ksyun.gov.fd.common.service.api.file.entity.vo.FileUploadVo;
import com.ksyun.gov.titanic.userportal.config.thread.ThreadPoolConstant;
import com.ksyun.gov.titanic.userportal.config.thread.ThreadPoolManager;
import com.ksyun.gov.titanic.userportal.domain.constant.SymbolConstant;
import com.ksyun.gov.titanic.userportal.domain.entity.KnowledgeBaseFileZYPo;
import com.ksyun.gov.titanic.userportal.domain.entity.KnowledgeBaseManagementZYPo;
import com.ksyun.gov.titanic.userportal.domain.enums.FileEnum;
import com.ksyun.gov.titanic.userportal.domain.param.*;
import com.ksyun.gov.titanic.userportal.domain.vo.KnowledgeBaseFileVo;
import com.ksyun.gov.titanic.userportal.domain.vo.KnowledgeBaseVo;
import com.ksyun.gov.titanic.userportal.execute.AsynExecute;
import com.ksyun.gov.titanic.userportal.mapper.KnowledgeBaseFileZYMapper;
import com.ksyun.gov.titanic.userportal.service.KnowledgeBaseFileSyncService;
import com.ksyun.gov.titanic.userportal.service.KnowledgeBaseFileZYService;
import com.ksyun.gov.titanic.userportal.utils.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

import static com.ksyun.gov.titanic.userportal.service.KnowledgeBaseFileSyncService.PROJECT_PATH;

/**
 * <p>
 * 知识库与文件映射表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Service
@RequiredArgsConstructor
public class KnowledgeBaseFileImpl extends BaseTenantServiceImpl<KnowledgeBaseFileZYMapper, KnowledgeBaseFileZYPo> implements KnowledgeBaseFileZYService {
    private static final Logger LOGGER = LoggerFactory.getLogger(KnowledgeBaseFileImpl.class);
    private final ThreadPoolManager threadPoolManager;
    private final KnowledgeBaseFileSyncService knowledgeBaseFileSyncService;


    @Value("${fd-file.name-length:50}")
    private Integer fileNameLength;

    @Override
    public List<Map<String, Object>> queryTotalByKbmCode() {
        return this.baseMapper.selectKnowledgeBaseFileGroupByCode();
    }

    @Override
    public void sharingLink(String sourceKbmCode, String targetKbmCode) {
        LambdaQueryWrapper<KnowledgeBaseFileZYPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBaseFileZYPo::getKbmCode, sourceKbmCode)
                .eq(KnowledgeBaseFileZYPo::getIsDeleted, 0);
        List<KnowledgeBaseFileZYPo> list = this.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            // 重新生成文件的code和parentCode
            Map<String, List<KnowledgeBaseFileZYPo>> listGroups = list.stream().collect(Collectors.groupingBy(KnowledgeBaseFileZYPo::getParentCode));
            List<KnowledgeBaseFileZYPo> knowledgeBaseFilePos = listGroups.get("0");
            for (KnowledgeBaseFileZYPo knowledgeBaseFilePo : knowledgeBaseFilePos) {
                String sourceParentCode = knowledgeBaseFilePo.getCode();
                knowledgeBaseFilePo.setId(null)
                        .setCode(GenerateCodeUtils.generateCode())
                        .setKbmCode(targetKbmCode);
                UserUtils.addCreatedBy(knowledgeBaseFilePo);
                if (FileEnum.DIR.getName().equalsIgnoreCase(knowledgeBaseFilePo.getType())) {
                    recursiveTraverSharingFile(sourceParentCode, knowledgeBaseFilePo.getCode(), listGroups, targetKbmCode);
                }
            }

            // 保存文件信息
            this.saveBatch(list);

            try {
                fileSyncDify(list);
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            }

        }
    }


    private void fileSyncDify(List<KnowledgeBaseFileZYPo> list) {
        List<Callable<Boolean>> tasks = new ArrayList<>();
        for (KnowledgeBaseFileZYPo knowledgeBaseFilePo : list) {
            if (StringUtils.equalsIgnoreCase(knowledgeBaseFilePo.getType(), FileEnum.FILE.getName())) {
                FileSyncParam fileSyncParam = new FileSyncParam();
                fileSyncParam.setId(knowledgeBaseFilePo.getId())
                        .setFileCode(knowledgeBaseFilePo.getOssFileCode())
                        .setUserId(knowledgeBaseFilePo.getCreatedBy())
                        .setFileName(knowledgeBaseFilePo.getFileName())
                        .setTenantId(knowledgeBaseFilePo.getTenantId())
                        .setCode(knowledgeBaseFilePo.getCode())
                        .setKbmCode(knowledgeBaseFilePo.getKbmCode())
                        .setCreatedBy(knowledgeBaseFilePo.getCreatedBy())
                        .setUpdatedBy(knowledgeBaseFilePo.getUpdatedBy());
                tasks.add(() -> {
                    knowledgeBaseFileSyncService.synFile(fileSyncParam);
                    return Boolean.TRUE;
                });
            }
        }

        // 同步文件到dify
        AsynExecute asynExecute = new AsynExecute(threadPoolManager.getThreadPoolExecutor(ThreadPoolConstant.FILE_SYNC_THREAD_POOL_NAME), null);
        asynExecute.execute(tasks, Boolean.TRUE);
    }

    @Override
    public void getChild(KnowledgeBaseFileVo knowledgeBaseFileVo) {
        if (StringUtils.equalsIgnoreCase(knowledgeBaseFileVo.getType(), FileEnum.DIR.getName())) {
            LambdaQueryWrapper<KnowledgeBaseFileZYPo> queryWrapper = new LambdaQueryWrapper<>();
            UserUtils.addTenantAndUserQueryColumn(queryWrapper);
            queryWrapper.eq(KnowledgeBaseFileZYPo::getParentCode, knowledgeBaseFileVo.getCode())
                    .eq(KnowledgeBaseFileZYPo::getIsDeleted,0)
                    .orderByAsc(KnowledgeBaseFileZYPo::getType)
                    .orderByDesc(KnowledgeBaseFileZYPo::getCreatedTime);
            List<KnowledgeBaseFileZYPo> list = this.list(queryWrapper);

            if (CollectionUtils.isNotEmpty(list)) {
                List<KnowledgeBaseFileVo> children = knowledgeBaseFileVo.getChildren();
                for (KnowledgeBaseFileZYPo knowledgeBaseFilePo : list) {
                    KnowledgeBaseFileVo fileVo = new KnowledgeBaseFileVo();
                    BeanUtils.copyProperties(knowledgeBaseFilePo, fileVo);
                    fileVo.setFileSizeFormat(FileSizeConverter.convertFileSize(fileVo.getFileSize()));
                    children.add(fileVo);
                    getChild(fileVo);
                }
            }

        }
    }

    @Override
    public PageRes<KnowledgeBaseFileVo> listKnowledgeBaseFile(KnowledgeBaseFilePageParam pageParam) {
        LambdaQueryWrapper<KnowledgeBaseFileZYPo> queryWrapper = new LambdaQueryWrapper<>();
        UserUtils.addTenantAndUserQueryColumn(queryWrapper);
        queryWrapper.like(StringUtils.isNotBlank(pageParam.getName()), KnowledgeBaseFileZYPo::getFileName, pageParam.getName())
                .eq(StringUtils.isNotBlank(pageParam.getLabel()), KnowledgeBaseFileZYPo::getLabel, pageParam.getLabel())
                .eq(KnowledgeBaseFileZYPo::getKbmCode, pageParam.getKdmpCode())
                .eq(KnowledgeBaseFileZYPo::getIsDeleted, 0)
                .orderByAsc(KnowledgeBaseFileZYPo::getType)
                .orderByDesc(KnowledgeBaseFileZYPo::getCreatedTime);

        PageReq pageReq = pageParam.getPageReq();
        Page<KnowledgeBaseFileZYPo> page = new Page<>(pageReq.getPageNum(), 99999);
        Page<KnowledgeBaseFileZYPo> filePage = this.page(page, queryWrapper);

        PageRes<KnowledgeBaseFileVo> resultPage = new PageRes<>();
        resultPage.setCurrent(pageReq.getPageNum());
        resultPage.setSize(pageReq.getPageSize());
        resultPage.setTotal(filePage.getTotal());
        List<KnowledgeBaseFileZYPo> records = filePage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return resultPage;
        }

        List<KnowledgeBaseFileVo> result = getKnowledgeBaseFileVos(records, "0");
        resultPage.setRecords(result);
        return resultPage;
    }




//    @Override
//    public List<KnowledgeBaseFileVo> getKnowledgeBaseFileVos(List<KnowledgeBaseFileZYPo> records, String parentCode) {
//        List<KnowledgeBaseFileVo> result = new ArrayList<>();
//        Map<String, KnowledgeBaseFileVo> memoryMap = new HashMap<>();
//        for (KnowledgeBaseFileZYPo record : records) {
//            KnowledgeBaseFileVo knowledgeBaseFileVo = new KnowledgeBaseFileVo();
//            BeanUtils.copyProperties(record, knowledgeBaseFileVo);
//            if (StringUtils.equalsIgnoreCase(record.getType(), FileEnum.DIR.getName())) {
//                memoryMap.put(record.getCode(), knowledgeBaseFileVo);
//            }
//            if (StringUtils.equalsIgnoreCase(record.getParentCode(), parentCode)) {
//                result.add(knowledgeBaseFileVo);
//            }
//        }
//
//        for (KnowledgeBaseFileZYPo record : records) {
//            if (!StringUtils.equalsIgnoreCase(record.getParentCode(), parentCode)) {
//                KnowledgeBaseFileVo knowledgeBaseFileVo = new KnowledgeBaseFileVo();
//                BeanUtils.copyProperties(record, knowledgeBaseFileVo);
//                KnowledgeBaseFileVo parent = getParent(knowledgeBaseFileVo, memoryMap, parentCode);
//                if (parent != null) {
//                    memoryMap.put(parent.getCode(), parent);
//                    if (!result.contains(parent)) {
//                        result.add(parent);
//                    }
//
//                }
//            }
//        }
//
//        // 格式化时间
//        formatFileSize(result);
//        return result;
//    }

    @Override
    public List<KnowledgeBaseFileVo> getKnowledgeBaseFileVos(List<KnowledgeBaseFileZYPo> records, String parentCode) {
        Map<String, KnowledgeBaseFileVo> memoryMap = new HashMap<>();
        for (KnowledgeBaseFileZYPo record : records) {
            KnowledgeBaseFileVo knowledgeBaseFileVo = new KnowledgeBaseFileVo();
            BeanUtils.copyProperties(record, knowledgeBaseFileVo);
            memoryMap.put(record.getCode(), knowledgeBaseFileVo);
        }

        List<KnowledgeBaseFileVo> result = buildFileHierarchy(records, parentCode, memoryMap);

        // 格式化时间
        formatFileSize(result);
        return result;
    }

    private List<KnowledgeBaseFileVo> buildFileHierarchy(List<KnowledgeBaseFileZYPo> records, String parentCode, Map<String, KnowledgeBaseFileVo> memoryMap) {
        List<KnowledgeBaseFileVo> result = new ArrayList<>();

        for (KnowledgeBaseFileZYPo record : records) {
            if (StringUtils.equalsIgnoreCase(record.getParentCode(), parentCode)) {
                KnowledgeBaseFileVo knowledgeBaseFileVo = memoryMap.get(record.getCode());
                if (StringUtils.equalsIgnoreCase(record.getType(), FileEnum.DIR.getName())) {
                    List<KnowledgeBaseFileVo> children = buildFileHierarchy(records, record.getCode(), memoryMap);
                    knowledgeBaseFileVo.setChildren(children);
                }
                result.add(knowledgeBaseFileVo);
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void shareContentToKnowledge(ContentShareParam param) {
        // 同步文件到文件服务
        String fileName = param.getQuestion().substring(0, Math.min(param.getQuestion().length(), fileNameLength)) + SymbolConstant.MARKDOWN_FORMAT;

        // 校验是否重名
        LambdaQueryWrapper<KnowledgeBaseFileZYPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBaseFileZYPo::getFileName, fileName)
                .eq(KnowledgeBaseFileZYPo::getKbmCode, param.getKbmCode())
                .eq(KnowledgeBaseFileZYPo::getParentCode, param.getParentCode())
                .eq(KnowledgeBaseFileZYPo::getIsDeleted, 0);
        List<KnowledgeBaseFileZYPo> filePos = this.list(queryWrapper);
        Assert.isTrue(CollectionUtils.isEmpty(filePos), "存在同名的文件或文件夹");

        String dirPath = PROJECT_PATH + File.separator + CodeGeneratorUtils.generate() + File.separator;
        String filePath = dirPath + fileName;
        File file = FIleUtils.saveFileByStr(param.getAnswer(), filePath);
        FileUploadVo fileUploadVo = knowledgeBaseFileSyncService.uploadFile(file);

        KnowledgeBaseFileZYPo knowledgeBaseFilePo = new KnowledgeBaseFileZYPo();
        knowledgeBaseFilePo.setLabel(param.getLabel())
                .setDescription(param.getDescription())
                .setKbmCode(param.getKbmCode())
                .setFileSize(Math.toIntExact(fileUploadVo.getFileSize()))
                .setFileName(fileName)
                .setType(FileEnum.FILE.getName())
                .setOssFileCode(fileUploadVo.getOssFileCode())
                .setParentCode(param.getParentCode())
                .setCode(GenerateCodeUtils.generateCode());
        UserUtils.addCreatedBy(knowledgeBaseFilePo);
        knowledgeBaseFilePo.setCreatedName(UserUtils.getUserInfo().getUserName());
        knowledgeBaseFilePo.setUpdatedName(UserUtils.getUserInfo().getUserName());

        this.save(knowledgeBaseFilePo);

        FileSyncParam fileSyncParam = new FileSyncParam();
        fileSyncParam.setId(knowledgeBaseFilePo.getId())
                .setFileCode(knowledgeBaseFilePo.getOssFileCode())
                .setUserId(knowledgeBaseFilePo.getCreatedBy())
                .setFileName(knowledgeBaseFilePo.getFileName())
                .setTenantId(knowledgeBaseFilePo.getTenantId())
                .setCode(knowledgeBaseFilePo.getCode())
                .setKbmCode(knowledgeBaseFilePo.getKbmCode())
                .setCreatedBy(knowledgeBaseFilePo.getCreatedBy())
                .setUpdatedBy(knowledgeBaseFilePo.getUpdatedBy());

        // 文件重命名
        String addSuffixToFileName = FIleUtils.addSuffixToFileName(fileSyncParam.getFileName(), String.valueOf(fileSyncParam.getId()));
        knowledgeBaseFileSyncService.shareContentToKnowledge(fileSyncParam, FIleUtils.saveFileByStr(param.getAnswer(), dirPath + addSuffixToFileName));

    }

    @Override
    public void knowledgeFileSyncDify(List<KnowledgeBaseFileZYPo> fileSyncDifyList) {
        if (CollectionUtils.isEmpty(fileSyncDifyList)) {
            return;
        }
        fileSyncDify(fileSyncDifyList);
    }

    private void formatFileSize(List<KnowledgeBaseFileVo> result) {
        for (KnowledgeBaseFileVo knowledgeBaseFileVo : result) {
            if (StringUtils.equalsIgnoreCase(knowledgeBaseFileVo.getType(), FileEnum.DIR.getName())) {
                knowledgeBaseFileVo.setFileSize(knowledgeBaseFileVo.getFileSize() + recursiveTraverFormatFileSize(knowledgeBaseFileVo.getChildren()));
            }
            knowledgeBaseFileVo.setFileSizeFormat(FileSizeConverter.convertFileSize(knowledgeBaseFileVo.getFileSize()));
        }
    }
    private int recursiveTraverFormatFileSize(List<KnowledgeBaseFileVo> result) {
        if (CollectionUtils.isEmpty(result)) {
            return 0;
        }
        for (KnowledgeBaseFileVo knowledgeBaseFileVo : result) {
            if (StringUtils.equalsIgnoreCase(knowledgeBaseFileVo.getType(), FileEnum.DIR.getName())) {
                knowledgeBaseFileVo.setFileSize(knowledgeBaseFileVo.getFileSize() + recursiveTraverFormatFileSize(knowledgeBaseFileVo.getChildren()));
            }
            knowledgeBaseFileVo.setFileSizeFormat(FileSizeConverter.convertFileSize(knowledgeBaseFileVo.getFileSize()));
        }
        return result.stream().mapToInt(KnowledgeBaseFileZYPo::getFileSize).sum();
    }


    private KnowledgeBaseFileVo getParent(KnowledgeBaseFileVo record, Map<String, KnowledgeBaseFileVo> memoryMap, String parentCode) {
        KnowledgeBaseFileVo fileVo = memoryMap.get(record.getParentCode());
        KnowledgeBaseFileVo parentVo = new KnowledgeBaseFileVo();
        if ( fileVo != null) {
            parentVo = fileVo;
        } else {
            LambdaQueryWrapper<KnowledgeBaseFileZYPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(KnowledgeBaseFileZYPo::getCode, record.getParentCode())
                    .eq(KnowledgeBaseFileZYPo::getIsDeleted, 0);
            KnowledgeBaseFileZYPo one = this.getOne(queryWrapper);
            if (one == null) {return null;}
            BeanUtils.copyProperties(one, parentVo);
            memoryMap.put(parentVo.getCode(), parentVo);
        }
        List<KnowledgeBaseFileVo> children = parentVo.getChildren();
        if (!children.contains(record)) {
            children.add(record);
        }
        if (StringUtils.equalsIgnoreCase(record.getType(), FileEnum.DIR.getName())) {
            memoryMap.put(record.getCode(), record);
        }
        if (StringUtils.equalsIgnoreCase(parentVo.getParentCode(), parentCode)) {
            return parentVo;
        } else {
            return getParent(parentVo, memoryMap, parentCode);
        }
    }


    private void recursiveTraverSharingFile(String sourceParentCode, String targetParentCode, Map<String, List<KnowledgeBaseFileZYPo>> listGroups, String targetKbmCode) {
        List<KnowledgeBaseFileZYPo> knowledgeBaseFilePos = listGroups.get(sourceParentCode);
        if (CollectionUtils.isEmpty(knowledgeBaseFilePos)) return;
        for (KnowledgeBaseFileZYPo knowledgeBaseFilePo : knowledgeBaseFilePos) {
            String childSourceParentCode = knowledgeBaseFilePo.getCode();
            knowledgeBaseFilePo.setId(null)
                    .setCode(GenerateCodeUtils.generateCode())
                    .setKbmCode(targetKbmCode)
                    .setParentCode(targetParentCode);
            UserUtils.addCreatedBy(knowledgeBaseFilePo);

            if (FileEnum.DIR.getName().equalsIgnoreCase(knowledgeBaseFilePo.getType())) {
                recursiveTraverSharingFile(childSourceParentCode, knowledgeBaseFilePo.getCode(), listGroups, targetKbmCode);
            }
        }
    }


    @Override
    public PageRes<KnowledgeBaseFileVo> listKnowledgeBaseFileByCode(KnowledgeBaseFileSearchPageParam param) {
        LambdaQueryWrapper<KnowledgeBaseFileZYPo> queryWrapper = new LambdaQueryWrapper<>();
        UserUtils.addTenantAndUserQueryColumn(queryWrapper);
        queryWrapper.like(StringUtils.isNotBlank(param.getName()), KnowledgeBaseFileZYPo::getFileName, param.getName())
                .eq(StringUtils.isNotBlank(param.getLabel()), KnowledgeBaseFileZYPo::getLabel, param.getLabel())
                .eq(StringUtils.isNotBlank(param.getType()), KnowledgeBaseFileZYPo::getType, param.getType())
                .eq(KnowledgeBaseFileZYPo::getKbmCode, param.getKdmpCode())
                .eq(StringUtils.isNotBlank(param.getCode()), KnowledgeBaseFileZYPo::getParentCode, param.getCode())
                .eq(KnowledgeBaseFileZYPo::getIsDeleted, 0)
                .orderByAsc(KnowledgeBaseFileZYPo::getType)
                .orderByDesc(KnowledgeBaseFileZYPo::getCreatedTime);
        if (!param.getStatus().isEmpty()){
            queryWrapper.eq(KnowledgeBaseFileZYPo::getStatus,param.getStatus());
        }
        // List<KnowledgeBaseFileZYPo> filePoList = fileService.list(queryWrapper);
        PageReq pageReq = param.getPageReq();
        Page<KnowledgeBaseFileZYPo> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        Page<KnowledgeBaseFileZYPo> kdmpPage = this.page(page, queryWrapper);
        List<KnowledgeBaseFileZYPo> records = kdmpPage.getRecords();

        List<KnowledgeBaseFileVo> result = new ArrayList<>();
        for (KnowledgeBaseFileZYPo knowledgeBaseFileZYPo : records) {
            KnowledgeBaseFileVo knowledgeBaseFileVo = new KnowledgeBaseFileVo();
            BeanUtils.copyProperties(knowledgeBaseFileZYPo, knowledgeBaseFileVo);
            if (StringUtils.equalsIgnoreCase(knowledgeBaseFileZYPo.getType(), FileEnum.DIR.getName())) {
                knowledgeBaseFileVo.setFileSizeFormat(FileSizeConverter.convertFileSize(recursiveTraversalFormatSize(knowledgeBaseFileVo.getCode())));
            } else {
                knowledgeBaseFileVo.setFileSizeFormat(FileSizeConverter.convertFileSize(knowledgeBaseFileVo.getFileSize()));
            }
            result.add(knowledgeBaseFileVo);
        }
        PageRes<KnowledgeBaseFileVo> resultPage = new PageRes<>();
        resultPage.setCurrent(pageReq.getPageNum());
        resultPage.setSize(pageReq.getPageSize());
        resultPage.setTotal(kdmpPage.getTotal());
        resultPage.setRecords(result);
        return resultPage;
    }

    private Integer recursiveTraversalFormatSize(String parentCode) {
        LambdaQueryWrapper<KnowledgeBaseFileZYPo> queryWrapper = new LambdaQueryWrapper<>();
        UserUtils.addTenantAndUserQueryColumn(queryWrapper);
        queryWrapper.eq(KnowledgeBaseFileZYPo::getParentCode, parentCode)
                .eq(KnowledgeBaseFileZYPo::getIsDeleted, 0);
        List<KnowledgeBaseFileZYPo> list = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        int dirSize = 0;
        for (KnowledgeBaseFileZYPo knowledgeBaseFilePo : list) {
            if(StringUtils.equalsIgnoreCase(knowledgeBaseFilePo.getType(), FileEnum.DIR.getName())) {
                dirSize += recursiveTraversalFormatSize(knowledgeBaseFilePo.getCode());
            } else {
                dirSize += knowledgeBaseFilePo.getFileSize();
            }
        }
        return dirSize;
    }
}
