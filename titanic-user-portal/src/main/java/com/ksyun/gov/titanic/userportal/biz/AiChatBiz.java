package com.ksyun.gov.titanic.userportal.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.ksyun.gov.fd.cloud.boot.util.Pager;
import com.ksyun.gov.fd.cloud.core.context.auth.AuthContextHolder;
import com.ksyun.gov.fd.cloud.core.web.model.LoginUser;
import com.ksyun.gov.fd.cloud.redis.client.FdStrRedisClient;
import com.ksyun.gov.fd.common.biz.PageRes;
import com.ksyun.gov.fd.common.json.JsonUtil;
import com.ksyun.gov.fd.common.lang.Safes;
import com.ksyun.gov.fd.common.lang.Yssert;
import com.ksyun.gov.titanic.ai.model.Message;
import com.ksyun.gov.titanic.userportal.biz.process.chat.AIChatContext;
import com.ksyun.gov.titanic.userportal.biz.process.chat.AISseEmitter;
import com.ksyun.gov.titanic.userportal.biz.process.chat.AiChatProcess;
import com.ksyun.gov.titanic.userportal.biz.process.chat.EmptySseEmitter;
import com.ksyun.gov.titanic.userportal.biz.process.memory.ChatMemoryStore;
import com.ksyun.gov.titanic.userportal.biz.process.memory.ChatRecord;
import com.ksyun.gov.titanic.userportal.common.biz.ChatUtils;
import com.ksyun.gov.titanic.userportal.common.biz.UserHandler;
import com.ksyun.gov.titanic.userportal.common.enums.KbCateEnum;
import com.ksyun.gov.titanic.userportal.config.aichat.AIClientConfig;
import com.ksyun.gov.titanic.userportal.config.aichat.ChatBIConfig;
import com.ksyun.gov.titanic.userportal.model.entity.*;
import com.ksyun.gov.titanic.userportal.model.param.*;
import com.ksyun.gov.titanic.userportal.model.vo.*;
import com.ksyun.gov.titanic.userportal.model.vo.user.AnswerFeedbackVo;
import com.ksyun.gov.titanic.userportal.service.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: xiaoshicheng
 * @date: 2024/1/30 16:20
 **/
@Component
@Slf4j
public class AiChatBiz {

    private static final Logger LOGGER = LoggerFactory.getLogger(AiChatBiz.class);

    private final AiChatService aiChatService;

    @Autowired
    private BotBiz botBiz;

    @Autowired
    private DictBiz dictBiz;

    @Autowired
    private List<AiChatProcess> aiChatProcesses;

    @Autowired
    private SysConfigBiz sysConfigBiz;

    private final UserHandler userHandler;

    @Autowired
    private AIClientConfig aiClientConfig;

    @Autowired
    private ChatMemoryStore chatMemoryStore;

    @Autowired
    private ChatRecordService chatRecordService;

    @Autowired
    private PromptTmplBiz promptTmplBiz;

    @Autowired
    private FeedbackBiz feedbackBiz;

    @Autowired
    private ChatBIConfig chatBIConfig;

    @Autowired
    private KbsLlmsBiz kbsLlmsBiz;

    @Autowired
    private KnowledgeBaseManagementService knowledgeBaseManagementService;

    @Autowired
    private KnowledgeBaseManagementBiz knowledgeBaseManagementBiz;

    @Autowired
    private KbsLlmsService kbsLlmsService;


    public AiChatBiz (AiChatService aiChatService, UserHandler userHandler) {
        this.aiChatService = aiChatService;
        this.userHandler = userHandler;
    }


    public AiChatRes chat (List<Message> messages) {
        return chat(messages, null);
    }

    public AiChatRes chat (List<Message> messages, String llmCode) {
        return chat(messages, llmCode, null);
    }

    public AiChatRes chat (List<Message> messages, String llmCode, Map<String, Object> vars) {
        return chat(messages, llmCode, null, vars, null);
    }

    public AiChatRes chat (List<Message> messages, String llmCode, Map<String, Object> vars, AISseEmitter emitter) {
        return chat(messages, llmCode, null, vars, null, null);
    }

    public AiChatRes chat (List<Message> messages, String llmCode, String botCode, Map<String, Object> vars, PromptContext promptContext) {
        return chat(messages, llmCode, botCode, vars, promptContext, null);
    }

    public AiChatRes chat (List<Message> messages, String llmCode, String botCode, Map<String, Object> vars, PromptContext promptContext, AISseEmitter emitter) {

        AiChatReq aiChatReq = new AiChatReq();
        aiChatReq.setLlmCode(llmCode);
        aiChatReq.setMessages(messages);
        aiChatReq.setBotCode(botCode);
        aiChatReq.setVariables(vars);
        aiChatReq.setPromptTemplate(promptContext);
        // 临时会话
        Option option = new Option();
        option.setEphemeral(true);
        aiChatReq.setOption(option);
        aiChatReq.setInitiatorId(UserHandler.getUserId());

        AISseEmitter emptySseEmitter = Optional.ofNullable(emitter).orElse(new EmptySseEmitter());
        CountDownLatch countDownLatch = new CountDownLatch(1);
        emptySseEmitter.onDone(countDownLatch::countDown);

        chatStreamV4(aiChatReq, emptySseEmitter);

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        AIChatContext aiChatContext = emptySseEmitter.getAiChatContext();
        String answer = ChatUtils.getAnswer(aiChatContext.getChatResList());
        AiChatRes last = Safes.last(aiChatContext.getChatResList());
        Optional.ofNullable(last)
                .map(aiChatRes -> Safes.first(last.getChoices()))
                .map(ChatChoiceVo::getDelta)
                .ifPresent(message -> message.setContent(answer));

        return last;

    }


    public void chatStreamV2 (AiChatReq aiChatReq, AISseEmitter emitter) {
        AIChatContext aiChatContext = null;

        try {
            aiChatContext = buildContext(aiChatReq);
            emitter.setAiChatContext(aiChatContext);
            LlmConfigVo llmConfig = aiChatContext.getLlmConfig();

            AiChatProcess first = Safes.of(aiChatProcesses).stream().filter(aiChatProcess -> {
                return aiChatProcess.support(llmConfig.getClientKey());

            }).findFirst().orElse(null);
            Yssert.notNull(first, "未找到对应的模型处理器");

            first.chat(aiChatContext, emitter);

        } catch (Exception e) {
            LOGGER.error("对话失败", e);
            emitter.sendErrorAndComplete(e.getMessage());
        }


    }

    public void chatStreamV3 (AiChatReq aiChatReq, AISseEmitter emitter) {
        String chatType = "simple_chat";


        try {

            // 助手信息回查
            if (StringUtils.isNotBlank(aiChatReq.getBotCode())) {
                BotDefVo botDetail = botBiz.getBotDetail(aiChatReq.getBotCode());
                Yssert.isTrue(Objects.nonNull(botDetail), "抱歉，该助手不存在");
                chatType = botDetail.getBotType();
            }

            switch (chatType) {
                case "simple_chat":
                    chatStreamV2(aiChatReq, emitter);
                    break;
                case "chat":
                    chatStreamV2(aiChatReq, emitter);
                    break;
                case "chatbi":
                    // todo
                    SpringUtil.getBean(ChatBIBiz.class).chat2sqlStreamV2(aiChatReq, emitter);
                    break;
                default:
                    // 不支持的类型，直接结束
                    emitter.sendDone();
            }

        } catch (Exception e) {
            LOGGER.error("对话失败", e);
            emitter.sendErrorAndComplete(e.getMessage());
        }


    }

    public void chatStreamV4 (AiChatReq aiChatReq, AISseEmitter emitter) {
        String chatType = "simple_chat";


        try {

            // 助手信息回查
            if (StringUtils.isNotBlank(aiChatReq.getBotCode())) {
                BotDefVo botDetail = botBiz.getBotDetail(aiChatReq.getBotCode());
                Yssert.isTrue(Objects.nonNull(botDetail), "抱歉，该助手不存在");
                chatType = botDetail.getBotType();
            }

            switch (chatType) {
                case "simple_chat":
                    chatStreamV2(aiChatReq, emitter);
                    break;
                case "chat":
                    chatStreamV2(aiChatReq, emitter);
                    break;
                case "chatbi":
                    chatStreamV2(aiChatReq, emitter);
                    break;
                default:
                    // 不支持的类型，直接结束
                    emitter.sendDone();
            }

        } catch (Exception e) {
            LOGGER.error("对话失败", e);
            emitter.sendErrorAndComplete(e.getMessage());
        }


    }


    public AIChatContext buildContext (AiChatReq aiChatReq) {
        // 轮次信息回查
        Yssert.isTrue(chatMemoryStore.verify(aiChatReq.getChatCode(), aiChatReq.getGroupCode()), "抱歉，该轮次编码为空或不存在对应实例。请开启新会话继续操作。");

        AIChatContext chatContext = AIChatContext.builder()
                .chatCode(aiChatReq.getChatCode())
                .aiChatReq(aiChatReq)
                .llmCode(aiChatReq.getLlmCode())
                .requestId(UUID.fastUUID().toString())
                .initiatorId(aiChatReq.getInitiatorId())
                .build();
        LOGGER.info("requestId:{}", chatContext.getRequestId());
        Message lastUserMessage = ChatUtils.getLastUserMessage(aiChatReq.getMessages());
        Yssert.notEmpty(lastUserMessage.getContent(), "输入不能为空");

        // 连续会话处理
        if (StringUtils.isNotBlank(chatContext.getChatCode())) {
            AiChatEntity chatByCode = aiChatService.getChatByCode(chatContext.getChatCode());
            Optional.ofNullable(chatByCode)
                    .map(AiChatEntity::getModel)
                    .ifPresent(model -> chatContext.setLlmConfig(aiClientConfig.getLLmConfigVo(model)));
        }

        // 助手信息回查
        if (StringUtils.isNotBlank(aiChatReq.getBotCode())) {
            BotDefVo botDetail = botBiz.getBotDetail(aiChatReq.getBotCode());
            Yssert.isTrue(Objects.nonNull(botDetail), "抱歉，该助手不存在");

            chatContext.setBotDefVo(botDetail);

            // 如果 LlmConfig 尚未设置，则从助手信息中填充
            if (Objects.isNull(chatContext.getLlmConfig())) {
                chatContext.setLlmConfig(aiClientConfig.getLLmConfigVo(botDetail.getLlmCode()));
            }
        }

        // 获取模型配置优先级：会话 > 助手 > 直接指定模型编码 > 默认模型
        if (Objects.isNull((chatContext.getLlmConfig()))) {
            String llmCode = aiChatReq.getLlmCode();
            chatContext.setLlmConfig(
                    StringUtils.isBlank(llmCode)
                            ? aiClientConfig.getDefaultLLmConfigVo()
                            : aiClientConfig.getLLmConfigVo(llmCode)
            );
        }

        Yssert.notNull(chatContext.getLlmConfig(), "抱抱歉，会话失败。未指定模型编码，请联系管理员。");
        Yssert.isTrue(chatContext.getLlmConfig().getStatus() == 1, "抱歉，当前模型已禁用，无法进行问答。");
        // 回填模型编码
        chatContext.setLlmCode(chatContext.getLlmConfig().getLlmCode());


        // prompt 处理
        if (Objects.nonNull(aiChatReq.getPromptTemplate())) {
            // 查询 prompt对象
            PromptTmplVo promptTmpl = promptTmplBiz.getTmplByCode(aiChatReq.getPromptTemplate().getPromptTemplateCode());
            Yssert.notNull(promptTmpl, "抱歉，该模板不存在:" + aiChatReq.getPromptTemplate().getPromptTemplateCode());

            aiChatReq.getPromptTemplate().setPromptTemplate(promptTmpl);
            chatContext.setPromptTemplate(aiChatReq.getPromptTemplate());
        }

        // 重新生成
        if (StringUtils.isNotBlank(aiChatReq.getGroupCode())) {
            Message last = Safes.last(chatContext.getAiChatReq().getMessages());
            last.setContent("请重新生成下列问题的回答：" + last.getContent());
        }

        return chatContext;
    }


    public PageRes<AiChatHistoryVo> listAiChatHistory (AiChatHistoryPageParam aiChatHistoryParam) {
        LambdaQueryWrapper<AiChatEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(aiChatHistoryParam.getBotCode()), AiChatEntity::getAssistantCode, aiChatHistoryParam.getBotCode())
                .like(StringUtils.isNotBlank(aiChatHistoryParam.getTitle()), AiChatEntity::getTitle, aiChatHistoryParam.getTitle())
                .orderByDesc(AiChatEntity::getLastUseTime)
                .eq(AiChatEntity::getCreatedBy, aiChatHistoryParam.getUserId());

        return Pager.of(aiChatService.page(new Page<>(aiChatHistoryParam.getPageNum(), aiChatHistoryParam.getPageSize()), queryWrapper), aiChatEntities -> {
            return Safes.of(aiChatEntities)
                    .stream()
                    .map(aiChatEntity -> {
                        AiChatHistoryVo aiChatHistoryVo = new AiChatHistoryVo();
                        aiChatHistoryVo.setChatCode(aiChatEntity.getChatCode());
                        aiChatHistoryVo.setCreatedTime(aiChatEntity.getCreatedTime());
                        aiChatHistoryVo.setTitle(aiChatEntity.getTitle());
                        aiChatHistoryVo.setBotCode(aiChatEntity.getAssistantCode());
                        aiChatHistoryVo.setModel(aiChatEntity.getModel());
                        return aiChatHistoryVo;
                    })
                    .collect(Collectors.toList());
        });
    }


    public String getChatIdByCode (String chatCode) {
        return Optional.ofNullable(aiChatService.getChatByCode(chatCode)).map(AiChatEntity::getChatId).orElse(null);
    }


    /**
     * 获取某会话的最新{limit}组问答案对 | 默认3组
     *
     * @param chatCode
     * @param limit
     * @return
     */
    public AiChatDetailVo listChatMsg (String chatCode, Integer limit) {
        ChatRecord chatRecord = new ChatRecord();
        chatRecord.setChatCode(chatCode);
        chatRecord.setBefore(Optional.ofNullable(limit).orElse(3));
        return chatMemoryStore.getMsgs(chatRecord);
    }


    public AiChatDetailVo listAiChatDetail (AiChatDetailParam aiChatDetailParam) {
        // 校验before和after参数不能同时出现
        Yssert.isFalse(Objects.nonNull(aiChatDetailParam.getBefore()) && Objects.nonNull(aiChatDetailParam.getAfter()), "参数有误。会话记录查询时只能选择一种查询方式(after|before)");

        ChatRecord chatRecord = new ChatRecord();
        BeanUtils.copyProperties(aiChatDetailParam, chatRecord);
        AiChatDetailVo result = chatMemoryStore.getMsgs(chatRecord);

        // 填充反馈信息
        List<String> AiMessageIds = Safes.of(result.getItems())
                .stream()
                .filter(itemVo -> Objects.equals(itemVo.getRole(), Message.Role.ASSISTANT.getName()))
                .map(ItemVo::getSegmentId)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(AiMessageIds)) {
            Map<String, AnswerFeedbackVo> answerFeedbackMap = feedbackBiz.feedbackByMsgIds(AiMessageIds);
            Safes.of(result.getItems())
                    // .stream()
                    // .filter(itemVo -> answerFeedbackMap.containsKey(itemVo.getSegmentId()))
                    .forEach(itemVo -> {
                        itemVo.setFeedbackVo(Optional.ofNullable(answerFeedbackMap.get(itemVo.getSegmentId())).orElseGet(() -> {
                            AnswerFeedbackVo answerFeedbackVo = new AnswerFeedbackVo();
                            answerFeedbackVo.setMsgFeedbackVo(new MessageFeedbackVo());
                            answerFeedbackVo.setObsoleteFileVos(new HashMap<>());
                            return answerFeedbackVo;
                        }));
                    });

        }

        // 获取会话知识库编码
        KnowledgeBaseManagementEntity chatKbm = knowledgeBaseManagementService.getByCode(aiChatDetailParam.getChatCode(), KnowledgeBaseManagementEntity::getName);
        if (Objects.nonNull(chatKbm)) {
            result.setChatKbCode(chatKbm.getKbmCode());
        }

        return result;
    }


    @Transactional(rollbackFor = Throwable.class)
    public Boolean delChatHistory (String chatCode) {
        Yssert.notEmpty(chatCode, "会话编码不能为空");

        try {
            // 删除知识库
            KnowledgeBaseManagementEntity kbmEntity = knowledgeBaseManagementService.getByCode(chatCode, KnowledgeBaseManagementEntity::getName);
            if (kbmEntity != null) {
                knowledgeBaseManagementBiz.deleteByKbmCode(kbmEntity.getKbmCode());
            }

            // 删除聊天记录
            aiChatService.remove(new LambdaQueryWrapper<AiChatEntity>().eq(AiChatEntity::getChatCode, chatCode));
            chatRecordService.remove(new LambdaQueryWrapper<ChatRecordEntity>().eq(ChatRecordEntity::getChatCode, chatCode));

            log.info("聊天历史删除成功: chatCode={}", chatCode);
        } catch (Exception e) {
            log.error("聊天历史删除失败: chatCode={}", chatCode, e);
            throw new RuntimeException("聊天历史删除失败", e);
        }

        return true;
    }

    public Boolean closeChat (String requestId) {
        return Safes.first(aiChatProcesses).closeChat(requestId);
    }


    public Boolean renameAiChatHistory (AiChatRenameParam req) {
        Integer titleLen = Optional.ofNullable(sysConfigBiz.getSysConfigByCache("sys.titleLen", AuthContextHolder.getLoginUser()
                        .getTenantId()))
                .map(SysConfigVO::getConfigValue).map(Integer::valueOf).orElse(10);
        Yssert.isFalse(req.getTitle().length() > titleLen, "标题长度不能超过" + titleLen + "。");

        AiChatEntity aiChatEntity = new AiChatEntity();
        aiChatEntity.setTitle(req.getTitle());

        return aiChatService.updateByCode(aiChatEntity, req.getChatCode(), AiChatEntity::getChatCode);
    }

    public Long messageFeedback (MessageFeedbackReq req) {
        Yssert.notEmpty(req.getGroupCode(), "编码不能为空");
        Yssert.notEmpty(req.getChatCode(), "编码不能为空");
        Yssert.notEmpty(req.getMessageCode(), "编码不能为空");
        Yssert.notNull(req.getRating(), "评分不能为空");
        Yssert.isTrue(1 <= req.getRating() && req.getRating() <= 5, "评分区间不正确。应为1-5分");

        MessageFeedbackEntity entity = new MessageFeedbackEntity();
        BeanUtils.copyProperties(req, entity);

        feedbackBiz.saveOrUpdateFeedback(entity);

        return entity.getId();
    }


    public List<String> nextRecommendation (String chatCode) {
        Yssert.notEmpty(chatCode, "会话编码为空，请输入有效的编码后重新请求。");
        AiChatEntity chatByCode = aiChatService.getChatByCode(chatCode);
        String botCode = chatByCode.getAssistantCode();
        BotDefVo botDetail = botBiz.getBotDetail(botCode);
        String botType = Optional.ofNullable(botDetail)
                .map(BotDefVo::getBotType)
                .orElse("");

        List<String> result = Lists.newArrayList();

        try {
            AiChatRes chat = null;
            Map<String, Object> vars = new HashMap<>();
            if (Objects.equals(botType, BotDefinitionEntity.BotType.CHATBI.getName())) {
                vars.put("dataModelCode", Objects.requireNonNull(botDetail).getDataModelVo().getDataModelCode());
                vars.put("curChatCode", chatCode);

                chat = chat(Lists.newArrayList(ChatUtils.buildUserMessage("1")), chatBIConfig.getNextRecommendationLlmCode(), vars);
            } else {
                PromptContext prompt = new PromptContext();
                prompt.setPromptTemplateCode("recommendQ");
                vars.put("latestQ", chatMemoryStore.modelInputs(chatCode, 3));

                chat = chat(Lists.newArrayList(ChatUtils.buildUserMessage("prompt")), null, botCode, vars, prompt);
            }

            String answer = ChatUtils.getAnswer(List.of(chat));

            // 正则取出JsonArray Str
            Matcher matcher = Pattern.compile("\\[.*?\\]").matcher(answer);
            if (!matcher.find()) return List.of();

            result = JSONUtil.toList(matcher.group(0), String.class);
        } catch (Exception e) {
            LOGGER.error("nextRecommendation ChatBI error chatCode:{}", chatCode, e);
            Yssert.throwEx(e.getMessage());
        }

        return result;
    }


    public List<String> nextRecommendationV2 (String chatCode) {
        Yssert.notEmpty(chatCode, "会话编码为空，请输入有效的编码后重新请求。");
        AiChatEntity chatByCode = aiChatService.getChatByCode(chatCode);
        String botCode = chatByCode.getAssistantCode();
        BotDefVo botDetail = botBiz.getBotDetail(botCode);
        String botType = Optional.ofNullable(botDetail)
                .map(BotDefVo::getBotType)
                .orElse("");

        List<String> result = Lists.newArrayList();

        try {
            AiChatRes chat = null;
            Map<String, Object> vars = new HashMap<>();
            // 问数？
            boolean isChatBI = BotDefinitionEntity.BotType.CHATBI.getName().equals(botType);
            if (isChatBI)
                vars.put("dataModelCode", Objects.requireNonNull(botDetail).getDataModelVo().getDataModelCode());

            vars.put("chatCode", chatCode);
            vars.put("chatType", isChatBI ? "chatBI" : "chat");
            chat = chat(Lists.newArrayList(ChatUtils.buildUserMessage("1")), chatBIConfig.getNextRecommendationLlmCode(), vars);

            String answer = ChatUtils.getAnswer(List.of(chat));

            // 正则取出JsonArray Str
            Matcher matcher = Pattern.compile("\\[.*?\\]").matcher(answer);
            if (!matcher.find()) return List.of();

            result = JSONUtil.toList(matcher.group(0), String.class);
        } catch (Exception e) {
            LOGGER.error("nextRecommendation ChatBI error chatCode:{}", chatCode, e);
            Yssert.throwEx(e.getMessage());
        }

        return result;
    }


    public List<ExpressQuestionVo> getExpressQuestions () {
        List<DictDataVo> dictDataList = dictBiz.getDictDataByCode("express_questions");
        List<ExpressQuestionVo> resultList = new ArrayList<>();
        for (DictDataVo dictDataVo : dictDataList) {
            ExpressQuestionVo expressQuestionVo = new ExpressQuestionVo();
            expressQuestionVo.setContent(dictDataVo.getDictValue());
            resultList.add(expressQuestionVo);
        }
        return resultList;
    }


    /**
     * 取消消息反馈
     *
     * @param id
     * @return
     */
    public Boolean msgFeedCancel (String id) {
        FeedbackVo vo = feedbackBiz.feedbackDetail(id);
        if (Objects.isNull(vo)) return false;

        // TODO 文件标记撤回
        if (vo.getType() == 1) return false;

        return feedbackBiz.delFeedback(id);
    }


    /**
     * 批量删除历史会话
     *
     * @param chatCodes
     * @return
     */
    @Transactional(rollbackFor = Throwable.class)
    public Boolean batchDelChatHistory (List<String> chatCodes, String userId) {
        Yssert.isFalse(CollUtil.isEmpty(chatCodes), "您传入会话编码为空。");

        LambdaQueryWrapper<AiChatEntity> qw = new LambdaQueryWrapper<AiChatEntity>().in(AiChatEntity::getChatCode, chatCodes).eq(AiChatEntity::getCreatedBy, userId);
        chatCodes = Safes.of(aiChatService.list(qw))
                .stream()
                .map(AiChatEntity::getChatCode)
                .toList();
        Yssert.isFalse(CollUtil.isEmpty(chatCodes), "您无权限操作传入会话编码对应的会话。");

        qw.clear();
        aiChatService.remove(qw.in(AiChatEntity::getChatCode, chatCodes));
        chatRecordService.remove(new LambdaQueryWrapper<ChatRecordEntity>().in(ChatRecordEntity::getChatCode, chatCodes));
        return true;
    }


    /**
     * Map<String, List<String>>
     *
     * @param chatCode
     * @param uid
     * @return
     */
    public Map<String, List<String>> thirdKbIdByChat (String chatCode, String uid) {
        LoginUser loginUser = AuthContextHolder.getLoginUser();
        if (loginUser == null) {
            LOGGER.warn("thirdKbIdByChat - User not logged in. chatCode: {}", chatCode);
            return Collections.emptyMap();
        }

        String currentUid = loginUser.getUid();
        String tenantId = loginUser.getTenantId();

        if (StringUtils.isBlank(chatCode)) return Collections.emptyMap();

        Map<String, List<String>> map = new ConcurrentHashMap<>();

        // 个人知识库
        // 注意：参数 uid 可能是调用方传入的特定用户ID，这里优先使用认证上下文中的用户ID currentUid
        if (StringUtils.isNotBlank(currentUid)) {
            FdStrRedisClient fdRedisClient = SpringUtil.getBean(FdStrRedisClient.class);
            String enabled = fdRedisClient.<String>get("enabledPkb" + chatCode);

            // 个人知识库开启
            if (Boolean.parseBoolean(enabled)) {
                String pkbCodesJson = fdRedisClient.get("pkbCodes" + chatCode);
                if (StringUtils.isNotBlank(pkbCodesJson)) {
                    List<String> pkbCodes = cn.hutool.json.JSONUtil.toList(pkbCodesJson, String.class);
                    if (CollectionUtil.isNotEmpty(pkbCodes)) {
                        LambdaQueryWrapper<KbsLlmsEntity> pkbQuery = new LambdaQueryWrapper<KbsLlmsEntity>()
                                .eq(KbsLlmsEntity::getLlmCode, "retrieve")
                                .in(KbsLlmsEntity::getKbmCode, pkbCodes)
                                .eq(KbsLlmsEntity::getTenantId, tenantId) // 按租户过滤
                                .eq(KbsLlmsEntity::getCreatedBy, currentUid); // 按用户过滤

                        List<String> llmKbCodes = kbsLlmsService.list(pkbQuery)
                                .stream()
                                .map(KbsLlmsEntity::getLlmKbCode)
                                .distinct()
                                .toList();
                        if (CollectionUtil.isNotEmpty(llmKbCodes)) {
                            map.put(KbCateEnum.PERSONAL_KB.getCode(), llmKbCodes);
                        }
                    }
                }
            }
        }

        // 外部知识库
        AiChatEntity chat = aiChatService.getChatByCode(chatCode);
        if (chat != null && StringUtils.isNotBlank(chat.getAssistantCode())) {
            BotDefinitionEntity bot = botBiz.botEntity(chat.getAssistantCode(), true);
            if (Objects.nonNull(bot) && StringUtils.isNotBlank(bot.getKbmCode())) {
                List<String> kbmCodes = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(bot.getKbmCode());
                if (CollectionUtil.isNotEmpty(kbmCodes)) {
                    LambdaQueryWrapper<KbsLlmsEntity> externalKbQuery = new LambdaQueryWrapper<KbsLlmsEntity>()
                            .eq(KbsLlmsEntity::getLlmCode, bot.getLlmCode())
                            .in(KbsLlmsEntity::getKbmCode, kbmCodes)
                            .eq(KbsLlmsEntity::getTenantId, tenantId);
                            // .eq(KbsLlmsEntity::getCreatedBy, currentUid); // 外部知识库通常与租户关联，不一定与单个用户关联，根据实际业务确定

                    List<String> thirdKbIds = Safes.of(kbsLlmsService.list(externalKbQuery))
                            .stream()
                            .map(KbsLlmsEntity::getLlmKbCode)
                            .distinct()
                            .toList();
                    if (CollectionUtil.isNotEmpty(thirdKbIds)) {
                        map.put(KbCateEnum.EXTERNAL_KB.getCode(), thirdKbIds);
                    }
                }
            }
        }

        // 附件知识库 (通常是临时的，与当前会话/用户强相关)
        KnowledgeBaseManagementEntity kbmEntity = knowledgeBaseManagementService.getByCode(chatCode, KnowledgeBaseManagementEntity::getName);
        if (Objects.nonNull(kbmEntity)) {
            LambdaQueryWrapper<KbsLlmsEntity> tempKbQuery = new LambdaQueryWrapper<KbsLlmsEntity>()
                    .eq(KbsLlmsEntity::getLlmCode, "retrieve") // 通常附件知识库使用通用检索模型
                    .eq(KbsLlmsEntity::getKbmCode, kbmEntity.getKbmCode())
                    .eq(KbsLlmsEntity::getTenantId, tenantId)
                    .eq(KbsLlmsEntity::getCreatedBy, currentUid); // 附件知识库与创建用户绑定

            List<String> thirdKbIds = Safes.of(kbsLlmsService.list(tempKbQuery))
                    .stream()
                    .map(KbsLlmsEntity::getLlmKbCode)
                    .distinct()
                    .toList();
            if (CollectionUtil.isNotEmpty(thirdKbIds)) {
                map.put(KbCateEnum.TEMP_KB.getCode(), thirdKbIds);
            }
        }
        return map;
    }


    /**
     * 获取会话绑定模型编码
     *
     * @param chatCode
     * @return
     */
    public String llmCodeByChat (String chatCode) {
        AiChatEntity chat = aiChatService.getChatByCode(chatCode);
        Yssert.notNull(chat, "会话不存在，会话编码：" + chatCode);

        return chat.getModel();
    }

}
