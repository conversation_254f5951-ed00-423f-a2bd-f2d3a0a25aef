package com.ksyun.gov.titanic.userportal.model.entity;

import lombok.Data;

import java.util.List;


@Data
public class KnowledgeBaseFileAbstractInfo {

    /**
     * 文件摘要信息
     *
     * {
     *       "summary": "本文档详细规定了企业软件在开发阶段通过ezone流水线平台打包Docker镜像的安全要求，涵盖镜像构建、存储和分发的全流程，确保镜像的安全性和合规性。",
     *       "tags": {
     *         "dates": [“20250501”],
     *         "locations": ["北京小米科技园"],
     *         "organizations": [
     *           "ezone流水线平台",
     *           "Harbor"
     *         ],
     *         "persons": [“开发1”],
     *         "topics": [
     *           "Docker镜像",
     *           "镜像分发"
     *         ]
     *       }
     *     }
     */

    private String summary = "";
    private Tags tags = new Tags();
    @Data
    public static class Tags {
        private List<String> dates = List.of();
        private List<String> locations = List.of();
        private List<String> organizations = List.of();
        private List<String> persons = List.of();
        private List<String> topics = List.of();
    }
}
