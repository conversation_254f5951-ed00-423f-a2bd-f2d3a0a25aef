package com.ksyun.gov.titanic.userportal.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.ksyun.gov.fd.cloud.boot.mybatis.BaseTenantServiceImpl;
import com.ksyun.gov.fd.common.biz.PageReq;
import com.ksyun.gov.fd.common.biz.PageRes;
import com.ksyun.gov.fd.common.service.api.user.entity.dto.group.dept.Dept;
import com.ksyun.gov.fd.common.service.api.user.fegin.DeptMgtAPI;
import com.ksyun.gov.fd.common.web.exception.BaseException;
import com.ksyun.gov.titanic.userportal.config.thread.ThreadPoolConstant;
import com.ksyun.gov.titanic.userportal.config.thread.ThreadPoolManager;
import com.ksyun.gov.titanic.userportal.domain.entity.KnowledgeBaseFileZYPo;
import com.ksyun.gov.titanic.userportal.domain.entity.KnowledgeBaseManagementZYPo;
import com.ksyun.gov.titanic.userportal.domain.enums.FileEnum;
import com.ksyun.gov.titanic.userportal.domain.param.*;
import com.ksyun.gov.titanic.userportal.domain.vo.KnowledgeBaseFileVo;
import com.ksyun.gov.titanic.userportal.domain.vo.KnowledgeBaseVo;
import com.ksyun.gov.titanic.userportal.execute.AsynExecute;
import com.ksyun.gov.titanic.userportal.mapper.KnowledgeBaseManagementZYMapper;
import com.ksyun.gov.titanic.userportal.service.KnowledgeBaseFileSyncService;
import com.ksyun.gov.titanic.userportal.service.KnowledgeBaseFileZYService;
import com.ksyun.gov.titanic.userportal.service.KnowledgeBaseManagementZYService;
import com.ksyun.gov.titanic.userportal.utils.FileSizeConverter;
import com.ksyun.gov.titanic.userportal.utils.GenerateCodeUtils;
import com.ksyun.gov.titanic.userportal.utils.TimeUtils;
import com.ksyun.gov.titanic.userportal.utils.UserUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * <p>
 * 知识库管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Service
@RequiredArgsConstructor
public class KnowledgeBaseManagementImpl extends BaseTenantServiceImpl<KnowledgeBaseManagementZYMapper, KnowledgeBaseManagementZYPo> implements KnowledgeBaseManagementZYService {
    private static final Logger LOGGER = LoggerFactory.getLogger(KnowledgeBaseManagementImpl.class);
    private final KnowledgeBaseFileZYService fileService;
    private final KnowledgeBaseFileSyncService fileSyncService;
    private final TransactionTemplate transactionTemplate;
    private final ThreadPoolManager threadPoolManager;


    private final DeptMgtAPI deptMgtAPI;
    @Value("${fd-file.batch-size:50}")
    private Integer batchSize;

    @Value("${fd.auth.client.keycloak.realm}")
    private String realm;

    @Override
    public String addKnowledgeBase(KnowledgeParam param) {
        LambdaQueryWrapper<KnowledgeBaseManagementZYPo> queryWrapper = new LambdaQueryWrapper<>();
        UserUtils.addTenantAndUserQueryColumn(queryWrapper);
        queryWrapper.eq(KnowledgeBaseManagementZYPo::getName, param.getName())
                .eq(KnowledgeBaseManagementZYPo::getIsDeleted, 0);
        KnowledgeBaseManagementZYPo one = this.getOne(queryWrapper);
        if (one != null) {
            throw new BaseException("1", String.format("名为: %s 的知识库已存在", param.getName()));
        }
        KnowledgeBaseManagementZYPo po = new KnowledgeBaseManagementZYPo();
        BeanUtils.copyProperties(param, po);
        UserUtils.addCreatedBy(po);
        po.setCode(GenerateCodeUtils.generateCode());
        this.save(po);
        return po.getCode();
    }

    @Override
    public void deleteKnowledgeBase(String code) {
        LambdaUpdateWrapper<KnowledgeBaseManagementZYPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(KnowledgeBaseManagementZYPo::getCode, code)
                .set(KnowledgeBaseManagementZYPo::getIsDeleted, 1);
        this.update(updateWrapper);
    }

    @Override
    public PageRes<KnowledgeBaseVo> listKnowledgeBase(KnowledgeBasePageParam pageParam) {
        LambdaQueryWrapper<KnowledgeBaseManagementZYPo> queryWrapper = new LambdaQueryWrapper<>();
        UserUtils.addTenantAndUserQueryColumn(queryWrapper);
        // 状态非0对 启用和禁用筛选
        if (!pageParam.getStatus().equals(0)){
            queryWrapper.eq(KnowledgeBaseManagementZYPo::getStatus,pageParam.getStatus());
        }
        queryWrapper.like(StringUtils.isNotBlank(pageParam.getName()), KnowledgeBaseManagementZYPo::getName, pageParam.getName())
                .like(StringUtils.isNotBlank(pageParam.getClassification()), KnowledgeBaseManagementZYPo::getClassification, pageParam.getClassification())
                .eq(KnowledgeBaseManagementZYPo::getIsDeleted, 0)
                .orderByDesc(KnowledgeBaseManagementZYPo::getCreatedTime);

        // 查询所有得知识库得文件个数，以及文件总和
        List<Map<String, Object>> maps = fileService.queryTotalByKbmCode();
        // 查询知识库
        PageReq pageReq = pageParam.getPageReq();
        Page<KnowledgeBaseManagementZYPo> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        Page<KnowledgeBaseManagementZYPo> kdmpPage = this.page(page, queryWrapper);
        List<KnowledgeBaseManagementZYPo> records = kdmpPage.getRecords();
        List<KnowledgeBaseVo> resultRecords =new ArrayList<>(records.size());
        for (KnowledgeBaseManagementZYPo record : records) {
            Optional<Map<String, Object>> kbmCode = maps.stream().filter(map -> Objects.equals(record.getCode(), map.get("kbm_code"))).findFirst();
            KnowledgeBaseVo knowledgeBaseVo = new KnowledgeBaseVo();
            Dept deptDetailDTO = deptMgtAPI.deptDetail(record.getDeptId(), realm).getSuccessData();
            knowledgeBaseVo.setDept(deptDetailDTO);
            knowledgeBaseVo.setStatus(record.getStatus());
            knowledgeBaseVo.setClassification(record.getClassification());
            knowledgeBaseVo.setCategory(record.getCategory());
            knowledgeBaseVo.setName(record.getName());
            knowledgeBaseVo.setDescription(record.getDescription());
            knowledgeBaseVo.setCode(record.getCode());
            knowledgeBaseVo.setFiletotal(kbmCode.map(m -> m.get("file_count").toString()).orElse("0"));
            knowledgeBaseVo.setFilesize(FileSizeConverter.convertFileSize(Integer.valueOf(kbmCode.map(m -> m.get("total_file_size").toString()).orElse("0"))));
            // todo: 缺少助手数量
            knowledgeBaseVo.setDefinitionTotal("稍后完成");
            resultRecords.add(knowledgeBaseVo);

        }
        PageRes<KnowledgeBaseVo> resultPage = new PageRes<>();
        resultPage.setCurrent(pageReq.getPageNum());
        resultPage.setSize(pageReq.getPageSize());
        resultPage.setTotal(kdmpPage.getTotal());
        resultPage.setRecords(resultRecords);
        return resultPage;
    }

    @Override
    public String uploadFile(List<KnowledgeBaseFileZYParam> params) {
        //todo: 判断对知识库的操作权限
        List<Callable<FileSyncResponse>> tasks = new ArrayList<>();
        for (KnowledgeBaseFileZYParam param : params) {
            checkFileParam(param);
            tasks.add(() -> transactionTemplate.execute(status -> {
                try {
                    operateUploadFile(param);
                    return new FileSyncResponse().setFileName(param.getFileName())
                            .setStatus(Boolean.TRUE)
                            .setKbmCode(param.getKbmCode());
                } catch (Exception e) {
                    LOGGER.error("知识库文件保存失败，保存文件信息为：{}, 失败信息为：{}",
                            JSONUtil.toJsonStr(param), e.getMessage(), e);
                    String errorMessage = e.getMessage() != null
                            ? e.getMessage().substring(0, Math.min(e.getMessage().length(), 200))
                            : "未知错误";
                    status.setRollbackOnly(); // 标记回滚
                    return new FileSyncResponse().setFileName(param.getFileName())
                            .setKbmCode(param.getKbmCode())
                            .setStatus(Boolean.FALSE)
                            .setErrorMessage(errorMessage);
                }
            }));
        }
        System.out.println(tasks);
        System.out.println();
        AsynExecute asynExecute = new AsynExecute(threadPoolManager.getThreadPoolExecutor(ThreadPoolConstant.FILE_SYNC_THREAD_POOL_NAME), null);
        asynExecute.execute(tasks, Boolean.TRUE);
        return "成功";
    }

    private void checkFileParam(KnowledgeBaseFileZYParam param) {
        Assert.notBlank(param.getFileName(), "文件名称为空!");
        Assert.notBlank(param.getType(), "文件类型为空!");
        Assert.notBlank(param.getKbmCode(), "知识库code为空!");

    }

    private void operateUploadFile(KnowledgeBaseFileZYParam param) {
        Assert.isTrue(FileEnum.checkExist(param.getType()), () -> new RuntimeException(String.format("文件类型: %s不存在", param.getType())));
        if (FileEnum.FILE.getName().equalsIgnoreCase(param.getType()) && StringUtils.isBlank(param.getOssFileCode())) {
            throw new BaseException("1", "文件code为空!");
        }

        // 校验知识库是否存在
        checkKnowledgeIsExist(param.getKbmCode());
        // todo:第一次进来同步至kbs_llms表中（个人知识库）

        // 校验同一知识库、同一层级下是否存在同名的文件或文件夹
        LambdaQueryWrapper<KnowledgeBaseFileZYPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBaseFileZYPo::getKbmCode, param.getKbmCode())
                .eq(KnowledgeBaseFileZYPo::getParentCode, param.getParentCode())
                .eq(KnowledgeBaseFileZYPo::getFileName, param.getFileName())
                .eq(KnowledgeBaseFileZYPo::getIsDeleted, 0);
        UserUtils.addTenantAndUserQueryColumn(queryWrapper);

        List<KnowledgeBaseFileZYPo> list = fileService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            LOGGER.debug(String.format("同一层级下,已经存在名称为：%s 的文件", param.getFileName()));
            return;
        }


        KnowledgeBaseFileZYPo knowledgeBaseFilePo = new KnowledgeBaseFileZYPo();
        BeanUtils.copyProperties(param, knowledgeBaseFilePo);
        knowledgeBaseFilePo.setCode(GenerateCodeUtils.generateCode());
        UserUtils.addCreatedBy(knowledgeBaseFilePo);
        knowledgeBaseFilePo.setCreatedName(UserUtils.getUserInfo().getUserName());
        knowledgeBaseFilePo.setUpdatedName(UserUtils.getUserInfo().getUserName());

        fileService.save(knowledgeBaseFilePo);
        if (StringUtils.equalsIgnoreCase(param.getType(), FileEnum.FILE.getName())) {
            FileSyncParam fileSyncParam = new FileSyncParam();
            fileSyncParam.setId(knowledgeBaseFilePo.getId())
                    .setFileCode(knowledgeBaseFilePo.getOssFileCode())
                    .setUserId(knowledgeBaseFilePo.getCreatedBy())
                    .setFileName(knowledgeBaseFilePo.getFileName())
                    .setTenantId(knowledgeBaseFilePo.getTenantId())
                    .setCode(knowledgeBaseFilePo.getCode())
                    .setKbmCode(knowledgeBaseFilePo.getKbmCode())
                    .setCreatedBy(knowledgeBaseFilePo.getCreatedBy())
                    .setUpdatedBy(knowledgeBaseFilePo.getUpdatedBy());
            // 同步文件到知识库
            try {
                fileSyncService.synFile(fileSyncParam);
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            }
        }


    }


    @Override
    public PageRes<KnowledgeBaseFileVo> listKnowledgeBaseFile(KnowledgeBaseFilePageParam pageParam) {
        checkKnowledgeIsExist(pageParam.getKdmpCode());
        return fileService.listKnowledgeBaseFile(pageParam);

//        Map<String, List<KnowledgeBaseFilePo>> fileGroups = records.stream().collect(Collectors.groupingBy(KnowledgeBaseFilePo::getParentCode));
//        List<KnowledgeBaseFileVo> result = new ArrayList<>();
//        List<KnowledgeBaseFilePo> knowledgeBaseFilePos = fileGroups.get("0");
//        if (CollectionUtils.isNotEmpty(knowledgeBaseFilePos)) {
//            for (KnowledgeBaseFilePo knowledgeBaseFilePo : knowledgeBaseFilePos) {
//                KnowledgeBaseFileVo knowledgeBaseFileVo = new KnowledgeBaseFileVo();
//                BeanUtils.copyProperties(knowledgeBaseFilePo, knowledgeBaseFileVo);
//                if (FileEnum.DIR.getName().equalsIgnoreCase(knowledgeBaseFileVo.getType())) {
//                    recursiveTraversalFile(knowledgeBaseFileVo, fileGroups);
//                }
//                knowledgeBaseFileVo.setFileSizeFormat(FileSizeConverter.convertFileSize(knowledgeBaseFileVo.getFileSize()));
//                result.add(knowledgeBaseFileVo);
//            }
//        }
//
//        resultPage.setRecords(result);
//        return resultPage;
    }
    @Override
    public List<KnowledgeBaseFileVo> listKnowledgeBaseFileByCode(KnowledgeBaseFileSearchParam param) {
        LambdaQueryWrapper<KnowledgeBaseFileZYPo> queryWrapper = new LambdaQueryWrapper<>();
        UserUtils.addTenantAndUserQueryColumn(queryWrapper);
        queryWrapper.like(StringUtils.isNotBlank(param.getName()), KnowledgeBaseFileZYPo::getFileName, param.getName())
                .eq(StringUtils.isNotBlank(param.getLabel()), KnowledgeBaseFileZYPo::getLabel, param.getLabel())
                .eq(StringUtils.isNotBlank(param.getType()), KnowledgeBaseFileZYPo::getType, param.getType())
                .eq(KnowledgeBaseFileZYPo::getKbmCode, param.getKdmpCode())
                .eq(StringUtils.isNotBlank(param.getCode()), KnowledgeBaseFileZYPo::getParentCode, param.getCode())
                .eq(KnowledgeBaseFileZYPo::getIsDeleted, 0)
                .orderByAsc(KnowledgeBaseFileZYPo::getType)
                .orderByDesc(KnowledgeBaseFileZYPo::getCreatedTime);

        List<KnowledgeBaseFileZYPo> filePoList = fileService.list(queryWrapper);

        if (CollectionUtils.isEmpty(filePoList)) {
            return ListUtil.empty();
        }

        List<KnowledgeBaseFileVo> result = new ArrayList<>();
        for (KnowledgeBaseFileZYPo knowledgeBaseFilePo : filePoList) {
            KnowledgeBaseFileVo knowledgeBaseFileVo = new KnowledgeBaseFileVo();
            BeanUtils.copyProperties(knowledgeBaseFilePo, knowledgeBaseFileVo);
            if (StringUtils.equalsIgnoreCase(knowledgeBaseFilePo.getType(), FileEnum.DIR.getName())) {
                knowledgeBaseFileVo.setFileSizeFormat(FileSizeConverter.convertFileSize(recursiveTraversalFormatSize(knowledgeBaseFileVo.getCode())));
            } else {
                knowledgeBaseFileVo.setFileSizeFormat(FileSizeConverter.convertFileSize(knowledgeBaseFileVo.getFileSize()));
            }
            result.add(knowledgeBaseFileVo);
        }

        return result;
    }

    private Integer recursiveTraversalFormatSize(String parentCode) {
        LambdaQueryWrapper<KnowledgeBaseFileZYPo> queryWrapper = new LambdaQueryWrapper<>();
        UserUtils.addTenantAndUserQueryColumn(queryWrapper);
        queryWrapper.eq(KnowledgeBaseFileZYPo::getParentCode, parentCode)
                .eq(KnowledgeBaseFileZYPo::getIsDeleted, 0);
        List<KnowledgeBaseFileZYPo> list = fileService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        int dirSize = 0;
        for (KnowledgeBaseFileZYPo knowledgeBaseFilePo : list) {
            if(StringUtils.equalsIgnoreCase(knowledgeBaseFilePo.getType(), FileEnum.DIR.getName())) {
                dirSize += recursiveTraversalFormatSize(knowledgeBaseFilePo.getCode());
            } else {
                dirSize += knowledgeBaseFilePo.getFileSize();
            }
        }
        return dirSize;
    }

    @Override
    public void deleteFile(List<String> codes) {
        //todo: 判断对知识库的操作权限
        LambdaQueryWrapper<KnowledgeBaseFileZYPo> queryWrapper = new LambdaQueryWrapper<>();
        UserUtils.addTenantAndUserQueryColumn(queryWrapper);
        queryWrapper.in(KnowledgeBaseFileZYPo::getCode, codes)
                .eq(KnowledgeBaseFileZYPo::getIsDeleted, 0);

        List<KnowledgeBaseFileZYPo> list = fileService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<KnowledgeBaseFileZYPo> filePoList = new ArrayList<>();
        for (KnowledgeBaseFileZYPo knowledgeBaseFilePo : list) {
            if (StringUtils.equalsIgnoreCase(knowledgeBaseFilePo.getType(), FileEnum.DIR.getName())) {
                filePoList.addAll(getChainFile(knowledgeBaseFilePo.getCode()));
            }

            filePoList.add(knowledgeBaseFilePo);

        }

        List<String> fileCodes = filePoList.stream()
                .filter(e -> StringUtils.equalsIgnoreCase(e.getType(), FileEnum.FILE.getName()))
                .map(KnowledgeBaseFileZYPo::getCode).toList();

        List<String> knowledgeCodes = filePoList.stream().map(KnowledgeBaseFileZYPo::getCode).toList();
        LambdaUpdateWrapper<KnowledgeBaseFileZYPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(KnowledgeBaseFileZYPo::getCode, knowledgeCodes)
                        .eq(KnowledgeBaseFileZYPo::getIsDeleted, 0)
                        .set(KnowledgeBaseFileZYPo::getIsDeleted, 1);

        fileService.update(updateWrapper);

        // 删除dify中对应的文件
        ExecutorService threadPoolExecutor = threadPoolManager.getThreadPoolExecutor(ThreadPoolConstant.FILE_DELETE_THREAD_POOL_NAME);
        threadPoolExecutor.execute(() -> fileSyncService.deleteFile(fileCodes));
    }

    private List<KnowledgeBaseFileZYPo> getChainFile(String code) {
        LambdaQueryWrapper<KnowledgeBaseFileZYPo> queryWrapper = new LambdaQueryWrapper<>();
        UserUtils.addTenantAndUserQueryColumn(queryWrapper);
        queryWrapper.eq(KnowledgeBaseFileZYPo::getParentCode, code)
                .eq(KnowledgeBaseFileZYPo::getIsDeleted, 0);

        List<KnowledgeBaseFileZYPo> list = fileService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return ListUtil.empty();
        }
        List<KnowledgeBaseFileZYPo> filePoList = new ArrayList<>();
        for (KnowledgeBaseFileZYPo knowledgeBaseFilePo : list) {
            if (StringUtils.equalsIgnoreCase(knowledgeBaseFilePo.getType(), FileEnum.DIR.getName())) {
                filePoList.addAll(getChainFile(knowledgeBaseFilePo.getCode()));
            }
            filePoList.add(knowledgeBaseFilePo);
        }
        return filePoList;
    }

    @Override
    public KnowledgeBaseFileVo updateKnowledgeBaseFile(KnowledgeBaseFileUpdateParam param) {
        //todo: 判断对知识库的操作权限
        KnowledgeBaseFileZYPo one = fileService.getOne(new LambdaQueryWrapper<KnowledgeBaseFileZYPo>()
                .eq(KnowledgeBaseFileZYPo::getCode, param.getCode())
                .eq(KnowledgeBaseFileZYPo::getCreatedBy, UserUtils.getUserId())
                .eq(KnowledgeBaseFileZYPo::getIsDeleted, 0));

        Assert.notNull(one, "知识库文件不存在");
        Assert.notBlank(one.getKbmCode(), "知识库code为空");
        Assert.notBlank(one.getParentCode());

        LambdaQueryWrapper<KnowledgeBaseFileZYPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBaseFileZYPo::getFileName, param.getName())
                .ne(KnowledgeBaseFileZYPo::getCode, param.getCode())
                .eq(KnowledgeBaseFileZYPo::getKbmCode, one.getKbmCode())
                .eq(KnowledgeBaseFileZYPo::getParentCode, one.getParentCode())
                .eq(KnowledgeBaseFileZYPo::getIsDeleted, 0);
        List<KnowledgeBaseFileZYPo> filePos = fileService.list(queryWrapper);
        Assert.isTrue(CollectionUtils.isEmpty(filePos), "存在同名的文件或文件夹");

        one.setFileName(param.getName())
                .setLabel(param.getLabel())
                .setDescription(param.getDescription());
        fileService.updateById(one);

        KnowledgeBaseFileVo knowledgeBaseFileVo = new KnowledgeBaseFileVo();
        BeanUtils.copyProperties(one, knowledgeBaseFileVo);
        knowledgeBaseFileVo.setFileSizeFormat(FileSizeConverter.convertFileSize(knowledgeBaseFileVo.getFileSize()));
        fileService.getChild(knowledgeBaseFileVo);
        return knowledgeBaseFileVo;
    }



    private void recursiveTraversalFile(KnowledgeBaseFileVo knowledgeBaseFileVo, Map<String, List<KnowledgeBaseFileZYPo>> fileGroups) {
        List<KnowledgeBaseFileZYPo> knowledgeBaseFilePos = fileGroups.get(knowledgeBaseFileVo.getCode());
        if (CollectionUtils.isNotEmpty(knowledgeBaseFilePos)) {
            List<KnowledgeBaseFileVo> result = new ArrayList<>();
            for (KnowledgeBaseFileZYPo childFilePo : knowledgeBaseFilePos) {
                KnowledgeBaseFileVo childFileVo = new KnowledgeBaseFileVo();
                BeanUtils.copyProperties(childFilePo, childFileVo);
                if (FileEnum.DIR.getName().equalsIgnoreCase(childFileVo.getType())) {
                    recursiveTraversalFile(childFileVo, fileGroups);
                }
                childFileVo.setFileSizeFormat(FileSizeConverter.convertFileSize(childFileVo.getFileSize()));
                result.add(childFileVo);
            }
            knowledgeBaseFileVo.setChildren(result);
        }
    }
    private void recursiveTraversalDir(KnowledgeBaseFileVo knowledgeBaseFileVo, Map<String, List<KnowledgeBaseFileZYPo>> fileGroups) {
        List<KnowledgeBaseFileZYPo> knowledgeBaseFilePos = fileGroups.get(knowledgeBaseFileVo.getCode());
        if (CollectionUtils.isNotEmpty(knowledgeBaseFilePos)) {
            List<KnowledgeBaseFileVo> result = new ArrayList<>();
            for (KnowledgeBaseFileZYPo childFilePo : knowledgeBaseFilePos) {
                KnowledgeBaseFileVo childFileVo = new KnowledgeBaseFileVo();
                BeanUtils.copyProperties(childFilePo, childFileVo);
                if (FileEnum.DIR.getName().equalsIgnoreCase(childFileVo.getType())) {
                    recursiveTraversalFile(childFileVo, fileGroups);
                    knowledgeBaseFileVo.setFileSizeFormat(FileSizeConverter.convertFileSize(childFileVo.getFileSize()));
                    result.add(childFileVo);
                }

            }
            knowledgeBaseFileVo.setChildren(result);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean knowledgerBaseFileCopy(KnowledgeCopyParam param) {
        // 获取参数
        String fromCode = param.getFromCode();

        String toCode = param.getToCode();
        String toFileCode = param.getToFileCode();
        // 校验知识库是否存在
        KnowledgeBaseManagementZYPo formKnowledge = this.baseMapper.selectOne(new QueryWrapper<KnowledgeBaseManagementZYPo>().eq("code", fromCode));
        KnowledgeBaseManagementZYPo toKnowledge = this.baseMapper.selectOne(new QueryWrapper<KnowledgeBaseManagementZYPo>().eq("code", toCode));
        if(Objects.isNull(formKnowledge) ) {
            throw new BaseException("500", "源知识库不存在");
        }
        if(Objects.isNull(toKnowledge)) {
            throw new BaseException("500", "目标知识库不存在");
        }
        // 校验文件是否存在
        List<String> fromFileCodes = param.getFromFileCodes();
        for (String fromFileCode : fromFileCodes) {
            if(StringUtils.isBlank(fromFileCode)) {
                throw new BaseException("500", "源文件不能为空");
            }
            KnowledgeBaseFileZYPo formFile = fileService.getOne(new QueryWrapper<KnowledgeBaseFileZYPo>().eq("code", fromFileCode).eq("kbm_code", fromCode));
            if(Objects.isNull(formFile)) {
                throw new BaseException("500", "源文件不存在");
            }
        }
       // KnowledgeBaseFilePo formFile = fileService.getOne(new QueryWrapper<KnowledgeBaseFilePo>().eq("code", fromFileCode));
        if(StringUtils.isNotBlank(toFileCode)){
            KnowledgeBaseFileZYPo toFile = fileService.getOne(new QueryWrapper<KnowledgeBaseFileZYPo>().eq("code", toFileCode));
            if(Objects.isNull(toFile)) {
                throw new BaseException("500", "目标文件夹不存在不存在");
            }
            // 校验目标只能是文件夹
            if (!toFile.getType().equals(FileEnum.DIR.getName())) {
                throw new BaseException("500", "目标只能是文件夹");
            }
        }
        List<KnowledgeBaseFileZYPo> fileSyncDifyList = new ArrayList<>();
        for (String fromFileCode : fromFileCodes){
            KnowledgeBaseFileZYPo formFile = fileService.getOne(new QueryWrapper<KnowledgeBaseFileZYPo>().eq("code", fromFileCode).eq("kbm_code", fromCode));
            // 跟据来源文件分类进行复制操作，如果是目录
            if(formFile.getType().equals(FileEnum.DIR.getName())){
                // 复制目录
                List<KnowledgeBaseFileZYPo> filePos= this.listKnowledgeBaseFileForParentCode(fromCode, fromFileCode);
//                if(filePos.size() == 0){
//                   formFile.setParentCode(toFileCode);
//                   formFile
//                }
                if(StringUtils.isBlank(toFileCode)){
                    toFileCode = "0";
                }
                // 把所有得文件插入一遍
                formFile.setParentCode(toFileCode);
                filePos.add(formFile);
                // 生成新的code
                Map<String,String> newCodes = filePos.stream().collect(Collectors.toMap(KnowledgeBaseFileZYPo::getCode, m -> GenerateCodeUtils.generateCode()));
                for (KnowledgeBaseFileZYPo file : filePos) {
                    file.setKbmCode(toCode);
                    file.setId(null);
                    if(formFile.getCode().equals(file.getCode())){
                        formFile.setParentCode(toFileCode);
                    }else {
                        file.setParentCode(newCodes.get(file.getParentCode()));
                    }
                    file.setCode(newCodes.get(file.getCode()));



                }
                fileService.saveBatch(filePos);

            }else if(formFile.getType().equals(FileEnum.FILE.getName())){
                formFile.setKbmCode(toCode);
                formFile.setId(null);
                formFile.setCode(GenerateCodeUtils.generateCode());
                if(StringUtils.isBlank(toFileCode)){
                    toFileCode = "0";
                }
                formFile.setParentCode(toFileCode);
                fileService.save(formFile);
                fileSyncDifyList.add(formFile);
            }
        }

        fileService.knowledgeFileSyncDify(fileSyncDifyList);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sharingLink(SharingLinkParam param) {
        Assert.notBlank(param.getKbmCode(), "分享知识库code为空!");
        // 校验时间是否过期
        if (TimeUtils.compareTime(param.getDeadline())) {
            throw new BaseException("1", "分享时间已过期!");
        }
        LambdaQueryWrapper<KnowledgeBaseManagementZYPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBaseManagementZYPo::getCode, param.getKbmCode())
                .eq(KnowledgeBaseManagementZYPo::getIsDeleted, 0);
        KnowledgeBaseManagementZYPo one = this.getOne(queryWrapper);
        Assert.isNull(one, "知识库不存在");
        one.setCode(GenerateCodeUtils.generateCode())
                .setId(null);
        UserUtils.addCreatedBy(one);
        this.save(one);

        fileService.sharingLink(param.getKbmCode(), one.getCode());
    }


    @Override
    public Boolean knowledgerBaseFileMove(KnowledgeCopyParam param) {
        // 获取参数
        String fromCode = param.getFromCode();

        String toCode = param.getToCode();
        String toFileCode = param.getToFileCode();
        // 校验知识库是否存在
        KnowledgeBaseManagementZYPo formKnowledge = this.baseMapper.selectOne(new QueryWrapper<KnowledgeBaseManagementZYPo>().eq("code", fromCode));
        KnowledgeBaseManagementZYPo toKnowledge = this.baseMapper.selectOne(new QueryWrapper<KnowledgeBaseManagementZYPo>().eq("code", toCode));
        if(Objects.isNull(formKnowledge) ) {
            throw new BaseException("500", "源知识库不存在");
        }
        if(Objects.isNull(toKnowledge)) {
            throw new BaseException("500", "目标知识库不存在");
        }
        List<KnowledgeBaseFileZYPo> fileSyncDifyList = new ArrayList<>();
        // 校验文件是否存在
        List<String> fromFileCodes = param.getFromFileCodes();
        for (String fromFileCode : fromFileCodes) {
            if(StringUtils.isBlank(fromFileCode)) {
                throw new BaseException("500", "源文件不能为空");
            }
            KnowledgeBaseFileZYPo formFile = fileService.getOne(new QueryWrapper<KnowledgeBaseFileZYPo>().eq("code", fromFileCode).eq("kbm_code", fromCode));
            if(Objects.isNull(formFile)) {
                throw new BaseException("500", "源文件不存在");
            }
        }
        // KnowledgeBaseFilePo formFile = fileService.getOne(new QueryWrapper<KnowledgeBaseFilePo>().eq("code", fromFileCode));
        if(StringUtils.isNotBlank(toFileCode)){
            KnowledgeBaseFileZYPo toFile = fileService.getOne(new QueryWrapper<KnowledgeBaseFileZYPo>().eq("code", toFileCode).eq("kbm_code", toCode));
            if(Objects.isNull(toFile)) {
                throw new BaseException("500", "目标文件夹不存在不存在");
            }
            // 校验目标只能是文件夹
            if (!toFile.getType().equals(FileEnum.DIR.getName())) {
                throw new BaseException("500", "目标只能是文件夹");
            }
        }
        for (String fromFileCode : fromFileCodes){
            KnowledgeBaseFileZYPo formFile = fileService.getOne(new QueryWrapper<KnowledgeBaseFileZYPo>().eq("code", fromFileCode).eq("kbm_code", fromCode));
            formFile.setKbmCode(toCode);
            // 跟据来源文件分类进行复制操作，如果是目录
            if(formFile.getType().equals(FileEnum.DIR.getName())){
                // 复制目录
                List<KnowledgeBaseFileZYPo> filePos= this.listKnowledgeBaseFileForParentCode(fromCode, fromFileCode);
                // 把所有得文件插入一遍
                formFile.setParentCode(toFileCode);
                filePos.add(formFile);
//                if(filePos.size() ==0){
//                    formFile.setParentCode(toFileCode);
//                    filePos.add(formFile);
//                }else{
                    for (KnowledgeBaseFileZYPo file : filePos) {
                        file.setKbmCode(toCode);
                        // file.setId(null);
                        if(formFile.getCode().equals(file.getCode())){
                            if(StringUtils.isBlank(toFileCode)){
                                file.setParentCode("0");
                            }else{
                                file.setParentCode(toFileCode);
                            }
                            // formFile.setParentCode(toFileCode);
                        }
                    }

                fileService.updateBatchById(filePos);

            }else if(formFile.getType().equals(FileEnum.FILE.getName())){
                formFile.setKbmCode(toCode);
                // formFile.setId(null);
                if(StringUtils.isBlank(toFileCode)){
                    formFile.setParentCode("0");
                }else{
                    formFile.setParentCode(toFileCode);
                }
                fileService.updateById(formFile);
                fileSyncDifyList.add(formFile);
            }
        }

        if (CollectionUtils.isNotEmpty(fileSyncDifyList)) {
            try {
                List<Callable<Boolean>> tasks = new ArrayList<>();
                List<List<KnowledgeBaseFileZYPo>> partition = Lists.partition(fileSyncDifyList, batchSize);
                for (List<KnowledgeBaseFileZYPo> knowledgeBaseFilePos : partition) {
                    tasks.add(() -> {
                        fileSyncService.updateAllMetadataInfo(knowledgeBaseFilePos);
                        return Boolean.TRUE;
                    });
                }
                AsynExecute asynExecute = new AsynExecute(threadPoolManager.getThreadPoolExecutor(ThreadPoolConstant.FILE_UPDATE_THREAD_POOL_NAME), null);
                asynExecute.execute(tasks, Boolean.FALSE);
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            }

        }

        return true;
    }

    @Override
    public void shareContentToKnowledge(ContentShareParam param) {
        checkKnowledgeIsExist(param.getKbmCode());
        fileService.shareContentToKnowledge(param);
    }

    public List<KnowledgeBaseFileZYPo> listKnowledgeBaseFileForParentCode(String knowledgeBaseCode, String parentCode) {
        LambdaQueryWrapper<KnowledgeBaseFileZYPo> queryWrapper = new LambdaQueryWrapper<>();
        UserUtils.addTenantAndUserQueryColumn(queryWrapper);
        // 查询知识库得文件跟据父级ID 知识库id
        queryWrapper
                .eq(KnowledgeBaseFileZYPo::getKbmCode, knowledgeBaseCode)
                .eq(KnowledgeBaseFileZYPo::getIsDeleted, 0)
                //.eq(StringUtils.isNotBlank(parentCode), KnowledgeBaseFilePo::getParentCode, parentCode)
                .orderByDesc(KnowledgeBaseFileZYPo::getCreatedTime);
        List<KnowledgeBaseFileZYPo> list = fileService.list(queryWrapper);
        // 查询出第一级得文件集合，然后递归查询子集
        // list.stream().collect(Collectors.toList())
        // 取得所有得父节点 然后进行递归查询子节点
        Map<String, List<KnowledgeBaseFileZYPo>> fileGroups = list.stream().collect(Collectors.groupingBy(KnowledgeBaseFileZYPo::getParentCode));

        List<KnowledgeBaseFileZYPo> finalresult = new ArrayList<>();
        // 查询根节点
        List<KnowledgeBaseFileZYPo> knowledgeBaseFilePos = fileGroups.get(parentCode);

        if (CollectionUtils.isNotEmpty(knowledgeBaseFilePos)) {
            //
            for (KnowledgeBaseFileZYPo knowledgeBaseFilePo : knowledgeBaseFilePos) {
                if (FileEnum.DIR.getName().equalsIgnoreCase(knowledgeBaseFilePo.getType())) {
                    // 递归查询子节点
                    recursiveTraversalFileForList(knowledgeBaseFilePo, fileGroups,finalresult);
                }
                finalresult.add(knowledgeBaseFilePo);
            }
        }
        return finalresult;
    }
    private void recursiveTraversalFileForList(KnowledgeBaseFileZYPo knowledgeBaseFilePo, Map<String, List<KnowledgeBaseFileZYPo>> fileGroups, List<KnowledgeBaseFileZYPo> finalresult) {
        List<KnowledgeBaseFileZYPo> knowledgeBaseFilePos = fileGroups.get(knowledgeBaseFilePo.getCode());
        if (CollectionUtils.isNotEmpty(knowledgeBaseFilePos)) {
            List<KnowledgeBaseFileZYPo> result = new ArrayList<>();
            for (KnowledgeBaseFileZYPo childFilePo : knowledgeBaseFilePos) {
                if (FileEnum.DIR.getName().equalsIgnoreCase(childFilePo.getType())) {
                    recursiveTraversalFileForList(childFilePo, fileGroups,finalresult);
                }
                result.add(childFilePo);
            }
            finalresult.addAll(result);
        }
    }

    private void checkKnowledgeIsExist(String kbmCode) {
        LambdaQueryWrapper<KnowledgeBaseManagementZYPo> queryWrapper = new LambdaQueryWrapper<>();
        UserUtils.addTenantAndUserQueryColumn(queryWrapper);
        queryWrapper.eq(KnowledgeBaseManagementZYPo::getCode, kbmCode)
                .eq(KnowledgeBaseManagementZYPo::getIsDeleted, 0);
        KnowledgeBaseManagementZYPo one = this.getOne(queryWrapper);
        Assert.notNull(one, "知识库不存在");
    }


    private void checkKnowledgePersonal(String kbmCode) {
        // todo: 用于判断知识库创建时建立知识库关系(个人知识库)
        LambdaQueryWrapper<KnowledgeBaseManagementZYPo> queryWrapper = new LambdaQueryWrapper<>();
        UserUtils.addTenantAndUserQueryColumn(queryWrapper);
        queryWrapper.eq(KnowledgeBaseManagementZYPo::getCode, kbmCode)
                .eq(KnowledgeBaseManagementZYPo::getIsDeleted, 0);
        KnowledgeBaseManagementZYPo one = this.getOne(queryWrapper);
        Assert.notNull(one, "知识库不存在");
    }
}
