package com.ksyun.gov.titanic.userportal.controller.openapi;

import com.ksyun.gov.fd.cloud.core.context.auth.AuthContext;
import com.ksyun.gov.fd.cloud.core.context.auth.AuthContextHolder;
import com.ksyun.gov.fd.cloud.core.web.model.LoginUser;
import com.ksyun.gov.fd.common.biz.PageRes;
import com.ksyun.gov.titanic.userportal.biz.BotBiz;
import com.ksyun.gov.titanic.userportal.biz.LabelingBiz;
import com.ksyun.gov.titanic.userportal.controller.LabelingChatBIController;
import com.ksyun.gov.titanic.userportal.model.param.BotDefQueryReq;
import com.ksyun.gov.titanic.userportal.model.vo.BotDefVo;
import com.ksyun.gov.titanic.userportal.model.vo.LabelingPreviewTableDataVO;
import com.ksyun.gov.titanic.userportal.model.vo.UserRecommendQ;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: yu
 * @description:
 * @create: 2024-12-21 13:47
 **/
@Slf4j
@RestController
@RequestMapping("/open/api/h5/temp")
@Tag(name = "移动端API-临时未对接用户开放")
public class AppH5TempController {


    @Autowired
    private BotBiz botBiz;

    /***
     *
     * 移动端暂时未对接用户，只能先用 open，下面的open都mock了用户
     */


    @Operation(summary = "获取助手详情", description = "获取助手详情")
    @GetMapping("/botdef/detail")
    public BotDefVo botDetail (String botCode) {
        mockLoginUser();
        return botBiz.getBotDetail(botCode, false);
    }


    @Autowired
    private LabelingBiz labelingBiz;

    @Operation(summary = "查询指定table数据", description = "门户首页模型数据预览")
    @PostMapping("labeling/tableQuery")
    public LabelingPreviewTableDataVO tableQuery (@RequestBody LabelingChatBIController.TableQueryParam param) {
        mockLoginUser();
        return labelingBiz.tableQuerySql(param);
    }

    @Operation(summary = "查询首页助手列表")
    @PostMapping("home/bot/pageDefList")
    public PageRes<BotDefVo> pageBotDefList (@RequestBody BotDefQueryReq req) {
        mockLoginUser();
        req.setAuth(true);
        req.setStatuses(List.of(3));
        return botBiz.pageDefList(req);
    }


    @Operation(summary = "获取某用户的推荐问题")
    @GetMapping("/recommendQ")
    public List<UserRecommendQ> getUserRecommendQ (@RequestParam(value = "botType", required = false) String botType) {
        mockLoginUser();
        return botBiz.getRecommendQ(botType);
    }

    public void mockLoginUser () {
        LoginUser loginUser = new LoginUser();
        loginUser.setUid("0c6191f4-e4d4-4d5b-a33d-e2fb71a332ea");
        loginUser.setTenantId("d878ccb4-f1ab-4708-978b-e954c22659aa");
        loginUser.setUserType("M");
        AuthContextHolder.set(new AuthContext(loginUser));
    }
}
