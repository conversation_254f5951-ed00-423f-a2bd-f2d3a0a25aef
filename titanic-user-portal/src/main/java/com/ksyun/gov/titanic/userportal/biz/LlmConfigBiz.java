package com.ksyun.gov.titanic.userportal.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dtflys.forest.Forest;
import com.ksyun.gov.fd.cloud.boot.util.Pager;
import com.ksyun.gov.fd.cloud.core.context.auth.AuthContextHolder;
import com.ksyun.gov.fd.cloud.redis.mq.RedisMQProducer;
import com.ksyun.gov.fd.common.biz.PageRes;
import com.ksyun.gov.fd.common.json.JsonUtil;
import com.ksyun.gov.fd.common.lang.Safes;
import com.ksyun.gov.fd.common.lang.Yssert;
import com.ksyun.gov.fd.common.service.api.auth.entity.req.auth.AuthReq;
import com.ksyun.gov.fd.common.service.api.auth.fegin.DataAuthAPI;
import com.ksyun.gov.fd.common.service.api.user.entity.dto.group.Tenant.TenantDTO;
import com.ksyun.gov.titanic.userportal.common.constant.LLMConstant;
import com.ksyun.gov.titanic.userportal.common.constant.SymbolConstant;
import com.ksyun.gov.titanic.userportal.config.aichat.AIClientLoader;
import com.ksyun.gov.titanic.userportal.model.entity.BotDefinitionEntity;
import com.ksyun.gov.titanic.userportal.model.entity.KbsLlmsEntity;
import com.ksyun.gov.titanic.userportal.model.entity.KnowledgeBaseManagementEntity;
import com.ksyun.gov.titanic.userportal.model.entity.LlmConfigEntity;
import com.ksyun.gov.titanic.userportal.model.param.LlmConfigPageReq;
import com.ksyun.gov.titanic.userportal.model.param.LlmConfigReq;
import com.ksyun.gov.titanic.userportal.model.vo.LlmBindVo;
import com.ksyun.gov.titanic.userportal.model.vo.dify.DifyClientPageVo;
import com.ksyun.gov.titanic.userportal.model.vo.dify.DifyClientVo;
import com.ksyun.gov.titanic.userportal.model.vo.LlmConfigVo;
import com.ksyun.gov.titanic.userportal.model.vo.LlmOption;
import com.ksyun.gov.titanic.userportal.model.vo.dify.DifyKeyVo;
import com.ksyun.gov.titanic.userportal.model.vo.user.TenantVo;
import com.ksyun.gov.titanic.userportal.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Component
public class LlmConfigBiz {
    @Value("${dify.base_url}")
    private String baseUrl;

    @Value("${dify.tenant_id}")
    private String tenantId;

    @Autowired
    private LlmConfigService llmConfigService;

    @Autowired
    private BotDefinitionService botDefinitionService;

    @Autowired
    private AIClientLoader clientLoader;

    @Autowired
    private KbsLlmsService kbsLlmsService;

    @Autowired
    private KnowledgeBaseManagementService knowledgeBaseManagementService;

    @Autowired
    private UserServiceWarp userServiceWarp;

    @Autowired
    private DataAuthAPI dataAuthAPI;

    @Autowired
    private RedisMQProducer redisMQProducer;

    String llmConfig_update = "llmConfig_update";


    /**
     * 创建模型
     *
     * @param req
     * @return
     */
    public Long addLlmConfig (LlmConfigReq req, String operateId) {
        // 校验
        check(req);

        LlmConfigEntity entity = new LlmConfigEntity();
        BeanUtil.copyProperties(req, entity);
        entity.setCreatedBy(operateId);
        entity.setApiKeys(String.join(SymbolConstant.COMMA, req.getApiKeys()));
        if (CollUtil.isNotEmpty(req.getBizTypes()))
            entity.setBizTypes(String.join(SymbolConstant.COMMA, req.getBizTypes()) + SymbolConstant.COMMA);
        llmConfigService.save(entity);


        refreshAIClient(entity.getLlmCode());
        // 加载client
        redisMQProducer.send(llmConfig_update, entity.getLlmCode());

        return entity.getId();
    }


    public Boolean updateLlmConfig (LlmConfigReq req, String operateId) {
        // 校验
        check(req);

        LlmConfigEntity entity = new LlmConfigEntity();
        entity.setUpdatedBy(operateId);
        BeanUtil.copyProperties(req, entity);
        entity.setApiKeys(String.join(SymbolConstant.COMMA, req.getApiKeys()));
        // 业务类型更新
        List<String> bizTypes = req.getBizTypes();
        if (bizTypes != null) {
            entity.setBizTypes(bizTypes.isEmpty() ? "" : String.join(SymbolConstant.COMMA, bizTypes) + SymbolConstant.COMMA);
        }

        boolean b = llmConfigService.updateById(entity);

        refreshAIClient(entity.getLlmCode());
        redisMQProducer.send(llmConfig_update, entity.getLlmCode());
        return b;
    }


    private void check (LlmConfigReq req) {
        Yssert.isTrue(CollUtil.isEmpty(Safes.of(LLMConstant.getAllConstants()).stream().filter(x -> x.equals(req.getLlmCode())).toList()) ||
                (Objects.nonNull(req.getId()) && Objects.nonNull(llmConfigService.getById(req.getId()))), "您输入的模型编码与内置变量同名，请修改后重新调整。");

        if (llmConfigService.count(new LambdaQueryWrapper<LlmConfigEntity>().eq(LlmConfigEntity::getLlmName, req.getLlmName()).ne(Objects.nonNull(req.getId()), LlmConfigEntity::getId, req.getId())) > 0) {
            Yssert.throwEx("存在同名模型，请修改名称");
        }

        if (llmConfigService.count(new LambdaQueryWrapper<LlmConfigEntity>().eq(LlmConfigEntity::getLlmCode, req.getLlmCode()).ne(Objects.nonNull(req.getId()), LlmConfigEntity::getId, req.getId())) > 0) {
            Yssert.throwEx("模型编码不唯一，请修改编码");
        }

        Integer isDefault = req.getIsDefault();
        if (Objects.nonNull(isDefault) && isDefault == 1 && llmConfigService.count(new LambdaQueryWrapper<LlmConfigEntity>().eq(LlmConfigEntity::getIsDefault, 1).ne(Objects.nonNull(req.getId()), LlmConfigEntity::getId, req.getId())) > 0) {
            Yssert.throwEx("已存在默认模型，请修改");
        }

        if (!isJson(req.getLlmOption())) {
            Yssert.throwEx("详细配置格式错误");
        }

    }


    private Boolean isJson (String str) {
        try {
            JSONUtil.parseObj(str);
        } catch (Exception e) {
            return false;
        }
        return true;
    }


    public Boolean delLlmConfig (String code) {
        if (botDefinitionService.count(new LambdaQueryWrapper<BotDefinitionEntity>().eq(BotDefinitionEntity::getLlmCode, code).eq(BotDefinitionEntity::getIsDeleted, 0)) > 0) {
            Yssert.throwEx("此模型已被助手应用，不可删除");
        }

        if (kbsLlmsService.count(new LambdaQueryWrapper<KbsLlmsEntity>().eq(KbsLlmsEntity::getLlmCode, code)) > 0) {
            Yssert.throwEx("此模型尚存在绑定的知识库，不可删除");
        }

        // if (llmConfigService.getByCode(code, LlmConfigEntity::getLlmCode).getIsDefault() == 1) {
        //     Yssert.throwEx("平台默认模型不可删除");
        // }

        boolean b = llmConfigService.deleteByCode(code, LlmConfigEntity::getLlmCode);

        redisMQProducer.send(llmConfig_update, code);

        return b;
    }


    public LlmConfigVo getDefaultLLmConfigVo () {
        return llmConfigVo(llmConfigService.getByCode("1", LlmConfigEntity::getIsDefault));
    }


    public LlmConfigVo getLlmConfigById (String code) {
        LlmConfigEntity llmConfigEntity = llmConfigService.getByCode(code, LlmConfigEntity::getLlmCode);
        if (Objects.isNull(llmConfigEntity)) {
            return null;
        }

        return llmConfigVo(llmConfigEntity);
    }


    public PageRes<LlmConfigVo> pageLlmConfig (LlmConfigPageReq req) {
        LambdaQueryWrapper<LlmConfigEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(StringUtils.isNotBlank(req.getType()), LlmConfigEntity::getType, req.getType());
        qw.like(StringUtils.isNotBlank(req.getSearch()), LlmConfigEntity::getLlmCode, req.getSearch()).or().like(StringUtils.isNotBlank(req.getSearch()), LlmConfigEntity::getLlmName, req.getSearch());
        qw.orderByDesc(LlmConfigEntity::getSort);
        qw.orderByDesc(LlmConfigEntity::getCreatedTime);

        Page<LlmConfigEntity> page = llmConfigService.page(new Page<>(req.getPageNum(), req.getPageSize()), qw);
        return Pager.of(page, this::llmConfigVos);
    }


    public PageRes<LlmConfigVo> authPageLlmConfig (LlmConfigPageReq req) {
        TenantVo tenantVo = userServiceWarp.tenantDetail(AuthContextHolder.getLoginUser().getTenantId());
        LambdaQueryWrapper<LlmConfigEntity> qw = new LambdaQueryWrapper<>();
        String userId = tenantVo.getUserId();
        if (!userServiceWarp.isSuperAdmin(userId)) {
            AuthReq authReq = new AuthReq();
            authReq.setAppCode("ai");
            authReq.setOperateUId(userId);
            authReq.setTypes(List.of("llm"));
            Map<String, List<String>> authMap = dataAuthAPI.authList(authReq).getSuccessData();

            // 无权限则返回空列表
            if (CollUtil.isEmpty(authMap.get("llm"))) return PageRes.of(req.getPageNum(), req.getPageSize());

            // 根据权限过滤
            qw.in(LlmConfigEntity::getLlmCode, authMap.get("llm"));
        }
        List<String> bizTypes = req.getBizTypes();
        qw.eq(Objects.nonNull(req.getFilterBan()) && req.getFilterBan(), LlmConfigEntity::getStatus, 1);
        qw.eq(StringUtils.isNotBlank(req.getType()), LlmConfigEntity::getType, req.getType());
        qw.like(StringUtils.isNotBlank(req.getSearch()), LlmConfigEntity::getLlmCode, req.getSearch()).or().like(StringUtils.isNotBlank(req.getSearch()), LlmConfigEntity::getLlmName, req.getSearch());
        qw.orderByDesc(LlmConfigEntity::getSort);
        qw.orderByDesc(LlmConfigEntity::getCreatedTime);
        // 业务类型查询
        if (CollUtil.isNotEmpty(bizTypes)) {
            qw.and(wrapper -> {
                bizTypes.forEach(x -> wrapper.like(LlmConfigEntity::getBizTypes, x + SymbolConstant.COMMA));
            });
        }

        Page<LlmConfigEntity> page = llmConfigService.page(new Page<>(req.getPageNum(), req.getPageSize()), qw);
        return Pager.of(page, this::llmConfigVos);
    }


    public List<LlmConfigVo> llmConfigVos (List<LlmConfigEntity> list) {
        return Safes.of(list).stream().map(this::llmConfigVo).collect(Collectors.toList());
    }


    private LlmConfigVo llmConfigVo (LlmConfigEntity llmConfigEntity) {
        LlmConfigVo llmConfigVo = new LlmConfigVo();
        BeanUtil.copyProperties(llmConfigEntity, llmConfigVo, "llmOption");
        llmConfigVo.setApiKeys(Arrays.stream(llmConfigEntity.getApiKeys().split(SymbolConstant.COMMA)).collect(Collectors.toList()));

        // 业务类型
        String bizTypes = llmConfigEntity.getBizTypes();
        if (StringUtils.isBlank(bizTypes)) {
            llmConfigVo.setBizTypes(List.of());
        } else {
            String trimmedBizTypes = bizTypes.endsWith(SymbolConstant.COMMA) ? bizTypes.substring(0, bizTypes.length() - 1) : bizTypes;
            llmConfigVo.setBizTypes(Arrays.stream(trimmedBizTypes.split(SymbolConstant.COMMA)).toList());
        }

        if (StringUtils.isNotBlank(llmConfigEntity.getLlmOption())) {
            llmConfigVo.setLlmOption(JsonUtil.of(llmConfigEntity.getLlmOption(), LlmOption.class));
        }

        return llmConfigVo;
    }


    public List<LlmConfigEntity> listLlmConfigByCodes (Set<String> llmCodes) {
        LambdaQueryWrapper<LlmConfigEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(LlmConfigEntity::getLlmCode, llmCodes);
        return llmConfigService.list();
    }


    /**
     * 禁用|启用模型
     *
     * @param code
     * @param status
     * @return
     */
    @Transactional(rollbackFor = Throwable.class)
    public Boolean enable (String code, Integer status) {
        boolean enable = llmConfigService.update(new LambdaUpdateWrapper<LlmConfigEntity>().eq(LlmConfigEntity::getLlmCode, code).set(LlmConfigEntity::getStatus, status));
        refreshAIClient(code);
        return enable;
    }

    /**
     * 按照模型刷新
     *
     * @param llmCode
     */
    public void refreshAIClient (String llmCode) {
        LlmConfigEntity byCode = llmConfigService.getByCode(llmCode, LlmConfigEntity::getLlmCode);
        if (byCode == null) {
            clientLoader.unloadClient(List.of(llmCode));
            return;
        }
        LlmConfigVo llmConfigVo = llmConfigVo(byCode);
        Integer status = llmConfigVo.getStatus();
        if (Objects.equals(1, status)) {
            clientLoader.unloadClient(List.of(llmCode));
            clientLoader.loadClient(llmConfigVo);
        } else {
            clientLoader.unloadClient(List.of(llmCode));
        }
    }


    /**
     * Dify Client导入
     *
     * @return
     */
    public PageRes<LlmConfigVo> imports (LlmConfigPageReq req) {
        Map<Object, Object> map = new HashMap<>();
        map.put("tenant_id", tenantId);
        String keyStr = Forest.get(baseUrl + "/console/api/datasets/api-keys-by-tenant")
                .addQuery(map)
                .execute(String.class);

        Yssert.notEmpty(keyStr, "Dify知识库密钥为空，请预先设置。");

        List<DifyKeyVo> keyVos = JSONUtil.toList(JSONUtil.parseObj(keyStr).get("data", JSONArray.class), DifyKeyVo.class);
        Yssert.notEmpty(keyVos, "Dify知识库密钥为空，请预先设置。");
        // 知识库密钥
        String datasetKey = Safes.first(keyVos).getToken();

        map.put("limit", req.getPageSize());
        map.put("page", req.getPageNum());
        if (StringUtils.isNotBlank(req.getSearch())) map.put("name", req.getSearch());
        DifyClientPageVo pageVo = Forest.get(baseUrl + "/console/api/apps/app-details-with-api-keys")
                .addQuery(map)
                .execute(DifyClientPageVo.class);

        // Dify已导入的助手
        Map<String, LlmOption> appMap = Safes.of(llmConfigService.list(new LambdaQueryWrapper<LlmConfigEntity>().eq(LlmConfigEntity::getClientKey, "dify")))
                .stream()
                .filter(x -> Objects.nonNull(x.getLlmOption()))
                .map(x -> JSONUtil.toBean(x.getLlmOption(), LlmOption.class))
                .collect(Collectors.toMap(LlmOption::getAppId, Function.identity(), (x, y) -> y));

        return PageRes.of(pageVo.getPage(), pageVo.getLimit(), pageVo.getTotal(), llmConfigVos(pageVo.getData(), datasetKey, appMap));

    }


    public List<LlmConfigVo> llmConfigVos (List<DifyClientVo> clientVos, String datasetKey, Map<String, LlmOption> map) {
        return Safes.of(clientVos).stream().filter(x -> CollUtil.isNotEmpty(x.getApiKeys())).map(x -> {
            LlmConfigVo llmConfigVo = new LlmConfigVo();
            llmConfigVo.setLlmName(x.getName());
            llmConfigVo.setLlmCode(x.getId());
            llmConfigVo.setClientKey("dify");
            llmConfigVo.setBaseUrl(x.getApiBaseUrl());
            llmConfigVo.setIsDefault(0);
            llmConfigVo.setApiKeys(Arrays.asList(Safes.first(x.getApiKeys()).getToken(), datasetKey));
            llmConfigVo.setIsImport(map.containsKey(x.getId()) ? 1 : 0);
            LlmOption llmOption = new LlmOption();
            llmOption.setAppId(x.getId());
            llmConfigVo.setLlmOption(llmOption);
            return llmConfigVo;
        }).collect(Collectors.toList());
    }


    public Boolean batchAddClient (List<LlmConfigReq> req) {
        if (CollUtil.isEmpty(req)) {
            return Boolean.TRUE;
        }

        List<String> llmCodes = Safes.of(req).stream().map(LlmConfigReq::getLlmCode).collect(Collectors.toList());
        Yssert.isFalse(llmConfigService.count(new LambdaQueryWrapper<LlmConfigEntity>().in(LlmConfigEntity::getLlmCode, llmCodes)) > 0, "模型编码【llmCode】重复，选中模型的编码与已存在模型编码冲突，请修改后再次提交。");
        // 默认模型校验，留存备份
        // List<LlmConfigReq> defaultLlms = req.stream().filter(x -> x.getIsDefault() == 1).collect(Collectors.toList());
        // Yssert.isFalse(defaultLlms.size() > 1, "错误操作，新增多个默认模型客户端，仅允许存在1个默认模型。");
        // Yssert.isFalse(llmConfigService.count(new LambdaQueryWrapper<LlmConfigEntity>().eq(LlmConfigEntity::getIsDefault, 1)) > 0 && CollUtil.isNotEmpty(defaultLlms), "已存在默认模型，请编辑后重新提交。");

        List<LlmConfigEntity> clients = new ArrayList<>();
        req.forEach(x -> {
            LlmConfigEntity entity = new LlmConfigEntity();
            BeanUtil.copyProperties(x, entity);
            entity.setApiKeys(String.join(SymbolConstant.COMMA, x.getApiKeys()));
            clients.add(entity);

            // 加载client
            clientLoader.loadClient(llmConfigVo(entity));
        });

        llmConfigService.saveBatch(clients);
        return Boolean.TRUE;
    }


    /**
     * 获取模型引用列表
     *
     * @param llmCode
     * @return
     */
    public List<LlmBindVo> getRefer (String llmCode) {
        // 校验
        Yssert.isTrue(StringUtils.isNotBlank(llmCode) && llmConfigService.count(new LambdaQueryWrapper<LlmConfigEntity>().eq(LlmConfigEntity::getLlmCode, llmCode)) > 0, "模型编码不合法，请修改后重新请求。llmCode：" + llmCode);
        // 引用列表
        List<KbsLlmsEntity> kbsLlms = kbsLlmsService.listByLlmCode(llmCode);
        if (CollUtil.isEmpty(kbsLlms)) return null;

        List<KnowledgeBaseManagementEntity> kbms = knowledgeBaseManagementService.getByKbmCodes(kbsLlms.stream().map(KbsLlmsEntity::getKbmCode).toList());
        // 按照租户分组
        Map<String, List<KnowledgeBaseManagementEntity>> map = kbms
                .stream()
                .collect(Collectors.groupingBy(KnowledgeBaseManagementEntity::getTenantId));
        // 租户详情
        Map<String, TenantVo> tenantMap = userServiceWarp.tenantDetails(map.keySet().stream().toList())
                .stream()
                .collect(Collectors.toMap(TenantDTO::getId, Function.identity(), (x, y) -> x));

        return map.keySet().stream().map(x -> {
            LlmBindVo llmBindVo = new LlmBindVo();
            llmBindVo.setTenantDTO(tenantMap.get(x));
            llmBindVo.setKbmNames(map.get(x).stream().map(KnowledgeBaseManagementEntity::getName).toList());
            return llmBindVo;
        }).toList();
    }

}
