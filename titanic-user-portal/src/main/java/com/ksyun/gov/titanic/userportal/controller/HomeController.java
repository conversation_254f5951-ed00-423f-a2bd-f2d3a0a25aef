package com.ksyun.gov.titanic.userportal.controller;

import com.ksyun.gov.fd.cloud.core.context.auth.AuthContextHolder;
import com.ksyun.gov.fd.common.biz.PageRes;
import com.ksyun.gov.titanic.userportal.biz.BotBiz;
import com.ksyun.gov.titanic.userportal.biz.DictBiz;
import com.ksyun.gov.titanic.userportal.biz.KnowledgeBaseManagementBiz;
import com.ksyun.gov.titanic.userportal.annotation.NoAuthorize;
import com.ksyun.gov.titanic.userportal.model.param.BotDefQueryReq;
import com.ksyun.gov.titanic.userportal.model.param.KnowledgeBasePageParam;
import com.ksyun.gov.titanic.userportal.model.vo.BotDefVo;
import com.ksyun.gov.titanic.userportal.model.vo.DictDataVo;
import com.ksyun.gov.titanic.userportal.model.vo.KnowledgeBaseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: xiaoshicheng
 * @date: 2024/2/27 20:08
 **/
@RestController
@RequestMapping("/home")
//@NoAuthorize
@Tag(name = "home")
public class HomeController {
    private final KnowledgeBaseManagementBiz knowledgeBaseManagementBiz;
    private final DictBiz dictBiz;

    @Autowired
    private BotBiz botBiz;

    public HomeController (KnowledgeBaseManagementBiz knowledgeBaseManagementBiz, DictBiz dictBiz) {
        this.knowledgeBaseManagementBiz = knowledgeBaseManagementBiz;
        this.dictBiz = dictBiz;
    }

    @Operation(summary = "查询知识库列表")
    @PostMapping("/knowledge-base/page")
    public PageRes<KnowledgeBaseVo> listKnowledgeBaseVos (@Validated @RequestBody KnowledgeBasePageParam knowledgeBasePageParam) {
        AuthContextHolder.mockSystemSuperUser();
        return knowledgeBaseManagementBiz.listKnowledgeBaseVos(knowledgeBasePageParam);
    }

    @Operation(summary = "查询首页助手列表")
    @PostMapping("/bot/pageDefList")
    public PageRes<BotDefVo> pageBotDefList (@RequestBody BotDefQueryReq req) {
        req.setAuth(true);
        req.setStatuses(List.of(3));
        return botBiz.pageDefList(req);
    }

    @Operation(summary = "查询某个字典的值列表")
    @GetMapping("/listDictDataByCode")
    public List<DictDataVo> listDictDataByCode (String dictCode) {
        return dictBiz.getDictDataByCode(dictCode);
    }


}
