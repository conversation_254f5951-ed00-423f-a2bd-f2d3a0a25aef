package com.ksyun.gov.titanic.userportal.model.param.user;

import com.ksyun.gov.fd.common.service.api.validted.Update;
import com.ksyun.gov.titanic.userportal.model.Open;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @Description:
 * @Author: MYy
 * @Date: 2024/3/12
 **/
@Data
public class TenantUpdateParam {

    @NotEmpty(message = "租户Id不能为空", groups = {Update.class})
    @Schema(title = "ID")
    private String tenantId;

    @Schema(title = "租户编码")
    @NotEmpty(message = "租户Id不能为空")
    private String code;

    @NotEmpty(message = "称不能为空")
    @Schema(title = "名称")
    private String name;

    @Schema(title = "描述")
    private String description;

    @NotEmpty(message = "状态不能为空", groups = {Update.class})
    @Schema(title = "当前租户使用状态 -1：禁用，0：未禁用")
    private String state;

    @NotEmpty(message = "主账号不能为空", groups = {Update.class})
    @Schema(title = "租户主账户")
    private String userId;

    @Schema(title = "文库存储空间")
    private Double storageSpace;

    @NotEmpty(message = "操作人Id不为空", groups = {Open.class})
    @Schema(title = "操作人Id")
    private String operateUId;

    @Schema(title = "有效期开始时间")
    private String validFrom;

    @Schema(title = "有效期结束时间")
    private String validTo;

}
