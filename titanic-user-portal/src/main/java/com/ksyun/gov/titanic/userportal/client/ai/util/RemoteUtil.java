package com.ksyun.gov.titanic.userportal.client.ai.util;

import com.dtflys.forest.http.ForestResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ksyun.gov.fd.common.lang.Yssert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.function.Supplier;

/**
 * @Description:
 * @Author: MYy
 * @Date: 2024/3/22
 **/
public class RemoteUtil {

    private static final Logger logger = LoggerFactory.getLogger(RemoteUtil.class);
    private static final String SUCCESS_CODE="200";
    private static final String FAIL_CODE="-200";

    public static <R extends BaseResp> R invokeThrowEx (Supplier<ForestResponse<R>> func, String msgPrefix) {
        ForestResponse<R> result = null;
        try {
            result = func.get();
            boolean success = result.isSuccess();
        } catch (Throwable t) {
            logger.error("RemoteUtils.invokeThrowEx", t);
            Yssert.throwEx(msgPrefix + "失败");
        }

        logger.info("result");
        logger.info(result.getContent());
        if (!result.isSuccess()) {
            Yssert.throwEx(msgPrefix + "失败 msg:" + result.readAsString());
        }

        if (!result.getResult().isSuccess()) {
            Yssert.throwEx(msgPrefix + "失败  msg: " + result.getResult().getMsg());
        }

        return result.getResult();

    }



    public static <R extends BaseResp> R invokeThrowExNotResultSuccess (Supplier<ForestResponse<R>> func, String msgPrefix) {
        ForestResponse<R> result = null;
        try {
            result = func.get();
            boolean success = result.isSuccess();
        } catch (Throwable t) {
            logger.error("RemoteUtils.invokeThrowEx", t);
            Yssert.throwEx(msgPrefix + "失败");
        }

        if (!result.isSuccess()) {
            Yssert.throwEx(msgPrefix + "失败 msg:" + result.readAsString());
        }


        return result.getResult();
    }

    public static <R extends BaseResp> List<R> invokeListThrowEx (Supplier<ForestResponse<List<R>>> func, String msgPrefix) {
        ForestResponse<List<R>> result = null;
        try {
            result = func.get();
            boolean success = result.isSuccess();
        } catch (Throwable t) {
            logger.error("RemoteUtils.invokeThrowEx", t);
            Yssert.throwEx(msgPrefix + "失败");
        }

        if (!result.isSuccess()) {
            Yssert.throwEx(msgPrefix + "失败 msg:" + result.readAsString());
        }

        return result.getResult();
    }


    public static void invokeVoidThrowEx (Supplier<ForestResponse> func, String msgPrefix) {
        ForestResponse result = null;

        try {
            result = func.get();
        } catch (Throwable t) {
            logger.error("RemoteUtils.invokeThrowEx", t);
            Yssert.throwEx(msgPrefix + "失败");
        }

        if (!result.isSuccess()) {
            Yssert.throwEx(msgPrefix + "失败 msg:" + result.readAsString());
        }
    }


    //
    // public static String  invokeThrowExResultString (Supplier<ForestResponse<String>> func, String msgPrefix) {
    //     ForestResponse<String> result = null;
    //     try {
    //         result = func.get();
    //         if("200".equals(result.toString().trim())){
    //             return "200";
    //         }
    //         Yssert.throwEx(msgPrefix + "失败"+",code:"+result);
    //     } catch (Throwable t) {
    //         logger.error("RemoteUtils.invokeThrowEx", t);
    //         Yssert.throwEx(msgPrefix + "失败");
    //     }
    //
    //     if (!result.isSuccess()) {
    //         Yssert.throwEx(msgPrefix + "失败 msg:" + result.readAsString());
    //     }
    //
    //     if (!result.getResult().isSuccess()) {
    //         Yssert.throwEx(msgPrefix + "失败  msg: " + result.getResult().getMsg());
    //     }
    //
    //
    //     return result.getResult();
    // }

}
