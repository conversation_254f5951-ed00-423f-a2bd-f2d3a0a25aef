package com.ksyun.gov.titanic.userportal.service;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ksyun.gov.fd.cloud.core.web.ActionResponse;
import com.ksyun.gov.fd.common.service.api.file.entity.vo.FileUploadVo;
import com.ksyun.gov.fd.common.service.api.file.fegin.FileServiceAPI;
import com.ksyun.gov.fd.common.web.exception.BaseException;
import com.ksyun.gov.fd.common.web.exception.BaseExceptionEnum;
import com.ksyun.gov.fd.common.web.exception.BaseRunTimeException;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.AddLLMKnowledgeMode;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.client.AddLLMKnowledgeParam;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.client.UpdateMetadataListInfoParam;
import com.ksyun.gov.titanic.userportal.client.service.AIService;
import com.ksyun.gov.titanic.userportal.common.enums.LlmEnum;
import com.ksyun.gov.titanic.userportal.config.FdFileConfig;
import com.ksyun.gov.titanic.userportal.domain.entity.DifyFileInfoZYPo;
import com.ksyun.gov.titanic.userportal.domain.entity.KnowledgeBaseFileZYPo;
import com.ksyun.gov.titanic.userportal.domain.param.FileSyncParam;
import com.ksyun.gov.titanic.userportal.model.entity.KbLlmFilesEntity;
import com.ksyun.gov.titanic.userportal.model.entity.KbsLlmsEntity;
import com.ksyun.gov.titanic.userportal.utils.CodeGeneratorUtils;
import com.ksyun.gov.titanic.userportal.utils.FIleUtils;
import com.ksyun.gov.titanic.userportal.utils.GenerateCodeUtils;
import com.ksyun.gov.titanic.userportal.utils.UserUtils;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/26 23:17
 */
@Service
@RequiredArgsConstructor
public class KnowledgeBaseFileSyncService {
    private static final Logger LOGGER = LoggerFactory.getLogger(KnowledgeBaseFileSyncService.class);
    public static final String PROJECT_PATH = System.getProperty("user.dir") + File.separator + "temp-file" + File.separator;

    private final FileServiceAPI fileServiceAPI;
    private final AIService aiService;
    private final DifyFileInfoService difyFileInfoService;
    private final FdFileConfig fdFileConfig;
    private final  KbsLlmsService kbsLlmsService;

    private final KbLlmFilesService kbLlmFilesService;



    public File downFileByCode (String ossFileCode, String fileName, String dirPath) {
        String path = dirPath + File.separator + fileName;
        Response response = fileServiceAPI.download(ossFileCode);
        FIleUtils.mkDirs(path);
        File file = new File(path);
        if (response.status() == 200) {
            try (InputStream inputStream = response.body().asInputStream();
                 FileOutputStream outputStream = new FileOutputStream(file)) {
                // 将响应体中的字节写入目标文件
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
                throw new BaseRunTimeException(BaseExceptionEnum.SYS_ERROR.getCode(), e.getMessage());
            }
        } else {
            LOGGER.error("-- file down response status: {}, body: {} --", response.status(), response.body());
            throw new BaseRunTimeException(BaseExceptionEnum.SYS_ERROR.getCode(), String.format("从文件服务下载文件失败，失败状态码: %s, 失败原因: %s", response.status(), response.body()));
        }
        return file;
    }

    public void synFile(FileSyncParam fileSyncParam) {
        String dirPath = PROJECT_PATH + CodeGeneratorUtils.generate();
        try {
            String addSuffixToFileName = FIleUtils.addSuffixToFileName(fileSyncParam.getFileName(), String.valueOf(fileSyncParam.getId()));
            File file = this.downFileByCode(fileSyncParam.getFileCode(), addSuffixToFileName, dirPath);
            LOGGER.info("- file sync param: {}", JSONUtil.toJsonStr(fileSyncParam));

            synFileToDify(fileSyncParam, file);

        } finally {
//            if (!Objects.isNull(file)) {
//                LOGGER.info("开始删除本地临时文件,路径: {}", file.getPath());
//                boolean deleteStatus = file.delete();
//                LOGGER.info("本地文件删除: {}", Objects.equals(deleteStatus, Boolean.TRUE) ? "成功" : "失败");
//            }

            // 删除目录
            FIleUtils.deleteDirectory(new File(dirPath));

        }
    }

    private void synFileToDify(FileSyncParam fileSyncParam, File file) {
        // 文件上传至dify
        AddLLMKnowledgeParam param = AddLLMKnowledgeParam.builder().authorityUserId(fileSyncParam.getUserId())
                .itemTagName(fileSyncParam.getTenantId())
                .kbcodeMetaDataKey(fileSyncParam.getKbmCode())
                .build();
        // 获取setid
        LambdaQueryWrapper<KbsLlmsEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KbsLlmsEntity::getKbmCode,fileSyncParam.getKbmCode());
        LOGGER.info("queryWrapper.eq(KbsLlmsEntity::getKbmCode,fileSyncParam.getKbmCode())");
        LOGGER.info(fileSyncParam.getKbmCode());
        String setId = kbsLlmsService.getOne(queryWrapper).getLlmKbCode();
        AddLLMKnowledgeMode addLLMKnowledgeMode = aiService.addLLMKnowledge(param, fileSyncParam.getCode(),file,setId);
        DifyFileInfoZYPo difyFileInfoPo = new DifyFileInfoZYPo();
        difyFileInfoPo.setKnowledgeFileCode(fileSyncParam.getCode())
                .setCode(GenerateCodeUtils.generateCode())
                .setDocumentId(addLLMKnowledgeMode.getDocumentId())
                .setDataSetId(addLLMKnowledgeMode.getDataSetId())
//                .setMetaDataId(addLLMKnowledgeMode.getMetaDataId())
//                .setMetaDataName(addLLMKnowledgeMode.getMetaDataName())
                .setCreatedBy(fileSyncParam.getCreatedBy());
        difyFileInfoPo.setUpdatedBy(fileSyncParam.getUpdatedBy());
        difyFileInfoService.save(difyFileInfoPo);

        // 同步数据至kb_files_llm_files中
        KbLlmFilesEntity kbLlmFilesEntity = new KbLlmFilesEntity();
        kbLlmFilesEntity.setFileCode(fileSyncParam.getCode());
        kbLlmFilesEntity.setKbmCode(fileSyncParam.getKbmCode());
        kbLlmFilesEntity.setLlmFileCode(addLLMKnowledgeMode.getDocumentId());
        kbLlmFilesEntity.setLlmKbCode(setId);
        kbLlmFilesEntity.setLlmCode(LlmEnum.RETRIEVE_LLM_CODE.getCode());
        kbLlmFilesEntity.setUpdatedBy(fileSyncParam.getUpdatedBy());
        kbLlmFilesEntity.setCreatedBy(fileSyncParam.getCreatedBy());
        kbLlmFilesEntity.setTenantId(fileSyncParam.getTenantId());
        kbLlmFilesService.save(kbLlmFilesEntity);

    }

    public FileUploadVo uploadFile(File file) {
        FileInputStream fileInputStream = null;
        try {
            fileInputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile(file.getName(),file.getName(),"", fileInputStream);
            ActionResponse<FileUploadVo> upload = fileServiceAPI.upload(multipartFile, fdFileConfig.getApplication(), fdFileConfig.getModule());
            if (!StringUtils.equalsIgnoreCase(upload.getCode(), "0")) {
                throw new BaseException("500", JSONUtil.toJsonStr(upload));
            }
            return upload.getData();
        } catch (IOException e) {
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }

    }

    public void shareContentToKnowledge(FileSyncParam fileSyncParam, File file) {
        synFileToDify(fileSyncParam, file);
    }

    public void deleteFile(List<String> codes) {
        LOGGER.info("- 开始删除dify文件信息，需要删除的知识库文件codes: {} -", codes);
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        LambdaQueryWrapper<DifyFileInfoZYPo> queryWrapper = new LambdaQueryWrapper<>();
        UserUtils.addTenantAndUserQueryColumn(queryWrapper);
        queryWrapper.in(DifyFileInfoZYPo::getKnowledgeFileCode, codes)
                .eq(DifyFileInfoZYPo::getIsDeleted, 0);

        List<DifyFileInfoZYPo> list = difyFileInfoService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, Set<String>> result = list.stream()
                .collect(Collectors.groupingBy(
                        DifyFileInfoZYPo::getDataSetId,
                        Collectors.mapping(
                                DifyFileInfoZYPo::getDocumentId,
                                Collectors.toSet()
                        )
                ));

        for (String dataSetId : result.keySet()) {
            aiService.batchDeleteDocument(dataSetId, result.get(dataSetId), dataSetId);
        }
        LambdaUpdateWrapper<DifyFileInfoZYPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(DifyFileInfoZYPo::getKnowledgeFileCode, codes)
                .eq(DifyFileInfoZYPo::getIsDeleted, 0)
                .set(DifyFileInfoZYPo::getIsDeleted, 1);
        difyFileInfoService.update(updateWrapper);
    }

    public void updateAllMetadataInfo(List<KnowledgeBaseFileZYPo> knowledgeBaseFilePos) {
        if (CollectionUtils.isEmpty(knowledgeBaseFilePos)) {
            return;
        }
        List<String> codes = knowledgeBaseFilePos.stream().map(KnowledgeBaseFileZYPo::getCode).collect(Collectors.toList());
        LOGGER.info("- 开始更新dify文件信息，需要更新的知识库文件codes: {} -", codes);

        LambdaQueryWrapper<DifyFileInfoZYPo> queryWrapper = new LambdaQueryWrapper<>();
        UserUtils.addTenantAndUserQueryColumn(queryWrapper);
        queryWrapper.in(DifyFileInfoZYPo::getKnowledgeFileCode, codes)
                .eq(DifyFileInfoZYPo::getIsDeleted, 0);

        List<DifyFileInfoZYPo> list = difyFileInfoService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        Map<String, String> knowledgeCodeMap = knowledgeBaseFilePos.stream().collect(Collectors.toMap(KnowledgeBaseFileZYPo::getCode, KnowledgeBaseFileZYPo::getKbmCode));
        for (DifyFileInfoZYPo difyFileInfoPo : list) {
            UpdateMetadataListInfoParam updateMetadataListInfoParam = new UpdateMetadataListInfoParam();
            updateMetadataListInfoParam.toBuilder()
                            .datasetId(knowledgeCodeMap.get(difyFileInfoPo.getKnowledgeFileCode()))
                            .documentId(difyFileInfoPo.getDocumentId());
            aiService.updateAllMetadataInfo(updateMetadataListInfoParam, difyFileInfoPo.getKnowledgeFileCode());
        }
    }
}
