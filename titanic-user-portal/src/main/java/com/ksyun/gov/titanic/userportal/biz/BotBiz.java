package com.ksyun.gov.titanic.userportal.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ksyun.gov.fd.cloud.boot.util.Pager;
import com.ksyun.gov.fd.cloud.core.context.auth.AuthContextHolder;
import com.ksyun.gov.fd.cloud.core.web.ActionResponse;
import com.ksyun.gov.fd.cloud.core.web.model.LoginUser;
import com.ksyun.gov.fd.common.biz.PageReq;
import com.ksyun.gov.fd.common.biz.PageRes;
import com.ksyun.gov.fd.common.lang.Safes;
import com.ksyun.gov.fd.common.lang.Yssert;
import com.ksyun.gov.fd.common.service.api.auth.entity.req.auth.AuthReq;
import com.ksyun.gov.fd.common.service.api.auth.entity.req.definition.ViewDefinitionReq;
import com.ksyun.gov.fd.common.service.api.auth.fegin.DataAuthAPI;
import com.ksyun.gov.fd.common.service.api.auth.fegin.DefinitionApi;
import com.ksyun.gov.fd.common.service.api.file.entity.vo.FileInfoVo;
import com.ksyun.gov.fd.common.service.api.file.entity.vo.FileUploadVo;
import com.ksyun.gov.fd.common.service.api.file.fegin.FileServiceAPI;
import com.ksyun.gov.titanic.userportal.common.biz.IdUtils;
import com.ksyun.gov.titanic.userportal.common.biz.ImageUtils;
import com.ksyun.gov.titanic.userportal.common.utils.RedisUtils;
import com.ksyun.gov.titanic.userportal.model.entity.BotDefinitionEntity;
import com.ksyun.gov.titanic.userportal.common.biz.UserHandler;
import com.ksyun.gov.titanic.userportal.model.entity.BotHeatEntity;
import com.ksyun.gov.titanic.userportal.model.entity.SceneDefEntity;
import com.ksyun.gov.titanic.userportal.model.param.*;
import com.ksyun.gov.titanic.userportal.model.vo.*;
import com.ksyun.gov.titanic.userportal.model.vo.user.UserDetailVo;
import com.ksyun.gov.titanic.userportal.service.BotDefinitionService;
import com.ksyun.gov.titanic.userportal.service.BotHeatService;
import com.ksyun.gov.titanic.userportal.service.SceneDefService;
import com.ksyun.gov.titanic.userportal.service.UserServiceWarp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: yu
 * @description:
 * @create: 2024-03-06 15:16
 **/

@Component
public class BotBiz {
    private static final Logger logger = LoggerFactory.getLogger(BotBiz.class);

    @Autowired
    private BotDefinitionService botDefinitionService;
    @Autowired
    private BotHeatService botHeatService;
    @Autowired
    private KnowledgeBaseManagementBiz knowledgeBaseManagementBiz;
    @Autowired
    private KbsLlmsBiz kbsLlmsBiz;

    @Autowired
    private DataAuthAPI dataAuthAPI;

    @Autowired
    private SceneDefBiz sceneDefBiz;

    @Autowired
    private SceneDefService sceneDefService;

    @Autowired
    private DataSourceBiz dataSourceBiz;

    @Autowired
    private FileServiceAPI fileServiceAPI;

    @Value("${fd.file_service.tmp_path}")
    private String tmpPath;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private LlmConfigBiz llmConfigBiz;

    @Autowired
    private SysConfigBiz sysConfigBiz;

    @Autowired
    private UserServiceWarp userServiceWarp;

    @Autowired
    private DefinitionApi definitionApi;


    public PageRes<BotDefVo> pageDefList (BotDefQueryReq req) {
        logger.info("BotBiz请求进来了");
        Yssert.isTrue(req.getPageSize() <= 200, "每页最大200条");

        LambdaQueryWrapper<BotDefinitionEntity> qw = new LambdaQueryWrapper<BotDefinitionEntity>()
                .eq(BotDefinitionEntity::getIsDeleted, 0)
                .eq(StringUtils.isNotBlank(req.getBotCategory()), BotDefinitionEntity::getBotCategory, req.getBotCategory())
                .like(StringUtils.isNotBlank(req.getKeyword()), BotDefinitionEntity::getBotName, req.getKeyword());

        // 助手类型查询
        List<String> botTypes = req.getBotTypes();
        if (CollUtil.isNotEmpty(botTypes)) {
            qw.in(BotDefinitionEntity::getBotType, botTypes);
        }

        // 状态查询
        List<Integer> statuses = req.getStatuses();
        if (CollUtil.isNotEmpty(statuses)) {
            qw.in(BotDefinitionEntity::getStatus, statuses);
        }

        UserHandler.addTenantQueryColumn(qw);
        if (!AuthContextHolder.getLoginUser().isSuperAdmin() && !AuthContextHolder.getLoginUser().isTenantAdmin() && req.getAuth()) {
            //            AuthReq authReq = new AuthReq();
            //            authReq.setOperateUId(AuthContextHolder.getLoginUser().getUid());
            //            authReq.setAppCode("ai");
            //            authReq.setTypes(List.of("assistant"));
            //
            //            List<String> assistant = dataAuthAPI.authList(authReq).getSuccessData().get("assistant");
            //            if (CollUtil.isEmpty(assistant)) {
            //                return PageRes.of(req.getPageNum(), req.getPageSize(), 0);
            //            }
            // todo: 重新获取权限
            ViewDefinitionReq viewDefinitionReq = new ViewDefinitionReq();
            viewDefinitionReq.setOperateUId(AuthContextHolder.getLoginUser().getUid());
            List<String> definitionCodes = definitionApi.ViewDefinition(viewDefinitionReq).getSuccessData();
            if (CollUtil.isEmpty(definitionCodes)) {
                return PageRes.of(req.getPageNum(), req.getPageSize(), 0);
            }
             qw.in(BotDefinitionEntity::getBotCode, definitionCodes);
        }
        Page<BotDefinitionEntity> entityPage;
        if (req.getHeat()) {
            entityPage = botDefinitionService.pageWithJoinHeat(new Page<>(req.getPageNum(), req.getPageSize()), qw);
        } else {
            qw.orderByDesc(BotDefinitionEntity::getId);
            entityPage = botDefinitionService.page(new Page<>(req.getPageNum(), req.getPageSize()), qw);
        }
        return Pager.of(entityPage, this::adaptBotDefVo);

    }


    public List<BotDefVo> adaptBotDefVo (List<BotDefinitionEntity> entities) {
        if (CollUtil.isEmpty(entities)) return List.of();
        List<String> kbmCodes = Safes.of(entities).stream().map(BotDefinitionEntity::getKbmCode).filter(StringUtils::isNotBlank).flatMap(s -> Safes.of(Splitter.on(",").omitEmptyStrings().trimResults().splitToList(s)).stream()).distinct().collect(Collectors.toList());
        KnowledgeBasePageParam knowledgeBasePageParam = new KnowledgeBasePageParam();
        Map<String, KnowledgeBaseVo> knowledgeBaseVoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(kbmCodes)) {
            PageReq pageReq = new PageReq();
            pageReq.setPageNum(1);
            pageReq.setPageSize(Safes.of(kbmCodes).size());
            knowledgeBasePageParam.setPageReq(pageReq);
            knowledgeBasePageParam.setKbmCodes(kbmCodes);
            knowledgeBasePageParam.setCategory("external");
            knowledgeBaseVoMap = Safes.of(knowledgeBaseManagementBiz.listKnowledgeBaseVos(knowledgeBasePageParam).getRecords())
                    .stream()
                    .collect(Collectors.toMap(KnowledgeBaseVo::getKbmCode, Function.identity()));
        }

        List<String> ossFileCodes = entities.stream().map(BotDefinitionEntity::getAvatarPath).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<FileInfoVo> fileInfoVos = Lists.partition(ossFileCodes, 100).stream().flatMap(strings -> {
            ActionResponse<List<FileInfoVo>> fileItems = fileServiceAPI.getFileItems(strings);
            List<FileInfoVo> data1 = fileItems.getData();
            return Safes.of(data1).stream();
        }).toList();

        // 构建文件映射关系
        Map<String, FileInfoVo> fileMap = fileInfoVos.stream().collect(Collectors.toMap(FileInfoVo::getCode, v -> v));

        // 收集用户Id并构建用户映射关系
        List<String> userIds = entities.stream()
                .flatMap(x -> Stream.of(x.getCreatedBy(), x.getUpdatedBy()))
                .filter(Objects::nonNull)
                .filter(s -> !s.trim().isEmpty())
                .distinct()
                .toList();
        Map<String, UserDetailVo> userMap = userServiceWarp.listUser(userIds);

        Map<String, KnowledgeBaseVo> finalKnowledgeBaseVoMap = knowledgeBaseVoMap;
        return Safes.of(entities)
                .stream()
                .map(entity -> {
                    BotDefVo vo = new BotDefVo();
                    BeanUtils.copyProperties(entity, vo);
                    // 场景
                    if (StringUtils.isNotBlank(entity.getSceneCode())) {
                        vo.setSceneDefVo(sceneDefBiz.getSceneDefByCode(entity.getSceneCode()));
                    }
                    // 数据模型
                    if (StringUtils.isNotBlank(entity.getDataModelCode())) {
                        DataModelVo modelById = dataSourceBiz.getModelById(entity.getDataModelCode());
                        vo.setDataModelVo(modelById);
                    }

                    vo.setAvatarFileUrl(Optional.ofNullable(fileMap.get(entity.getAvatarPath())).map(FileInfoVo::getUrl).orElse(""));
                    vo.setAvatarFileName(Optional.ofNullable(fileMap.get(entity.getAvatarPath())).map(FileInfoVo::getOriginalName).orElse(""));
                    vo.setBotAdvancedConfig(entity.toAdvancedConfig());
                    vo.setKnowledgeBaseList(Splitter.on(",").splitToList(Safes.of(entity.getKbmCode(), "")).stream().map(finalKnowledgeBaseVoMap::get).filter(Objects::nonNull).collect(Collectors.toList()));
                    vo.setLlmConfig(llmConfigBiz.getLlmConfigById(entity.getLlmCode()));

                    // 创建|更新人
                    UserDetailVo create = userMap.get(entity.getCreatedBy());
                    if (Objects.nonNull(create)) {
                        vo.setCreatedBy(create.getName());
                    }
                    UserDetailVo update = userMap.get(entity.getUpdatedBy());
                    if (Objects.nonNull(update)) {
                        vo.setCreatedBy(update.getName());
                    }

                    // 创建|更新时间
                    vo.setCreatedTime(DateFormatUtils.format(entity.getCreatedTime(), "yyyy-MM-dd HH:mm:ss"));
                    vo.setUpdatedTime(DateFormatUtils.format(entity.getUpdatedTime(), "yyyy-MM-dd HH:mm:ss"));


                    return vo;
                })
                .collect(Collectors.toList());
    }


    /**
     * 获取未逻辑删除的助手详情
     *
     * @param botCode
     * @return
     */
    public BotDefVo getBotDetail (String botCode) {
        return getBotDetail(botCode, true);
    }


    /**
     * 查询助手详情
     *
     * @param botCode
     * @param flag    标识是否筛选逻辑删除
     * @return
     */
    public BotDefVo getBotDetail (String botCode, Boolean flag) {
        BotDefinitionEntity entity = botEntity(botCode, flag);
        Yssert.notNull(entity, "抱歉，该助手不存在");
        return Safes.first(adaptBotDefVo(Lists.newArrayList(entity)));
    }


    /**
     * 查询未删除的助手实体
     *
     * @return
     */
    public BotDefinitionEntity botEntity (String botCode, Boolean flag) {
        if (StringUtils.isBlank(botCode)) return null;
        return botDefinitionService.getOne(new LambdaQueryWrapper<BotDefinitionEntity>().eq(BotDefinitionEntity::getBotCode, botCode).eq(flag, BotDefinitionEntity::getIsDeleted, 0));
    }


    /**
     * 助手创建
     *
     * @param req
     * @return
     */
    // @Transactional(rollbackFor = Throwable.class)
    public BotDefCreateVo saveBotDef (BotDefReq req) {
        // 校验
        validateBotDefRequest(req, true);

        // 处理头像（如果未上传则生成默认头像）
        processAvatarPath(req);

        // 预生成助手编码
        String botCode = IdUtils.generateId();

        // 预生成模型编码
        String llmCode = RandomUtil.randomString(8);

        // 保存模型配置
        LlmConfigReq llmReq = new LlmConfigReq();
        BeanUtils.copyProperties(req.getLlmConfig(), llmReq);
        llmReq.setLlmName(botCode);
        llmReq.setType("business");
        llmReq.setLlmCode(llmCode);
        llmReq.setIsDefault(0);

        llmConfigBiz.addLlmConfig(llmReq, AuthContextHolder.getLoginUser().getUid());

        // 保存助手定义
        BotDefinitionEntity botEntity = createBotDefinitionEntity(req, botCode, llmCode);
        botDefinitionService.save(botEntity);

        // 初始化热度记录
        BotHeatEntity botHeatEntity = new BotHeatEntity();
        botHeatEntity.setBotCode(botCode);
        botHeatEntity.setUsageTimes(0);
        botHeatService.save(botHeatEntity);

        // 同步知识库
        KnowledgeBaseSyncResult syncResult = syncKnowledgeBases(req, llmCode);
        updateBotKnowledgeBasesIfNeeded(botEntity, syncResult);

        // 返回创建结果
        return buildCreateResponse(botCode, syncResult);
    }


    /**
     * 助手更新
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Throwable.class)
    public BotDefCreateVo updateBotDef (BotDefReq req) {
        // 校验
        validateBotDefRequest(req, false);

        // 检查助手是否存在
        BotDefVo botDetail = getBotDetail(req.getBotCode());
        Yssert.notNull(botDetail, "助手不存在");

        // 更新模型配置
        LlmConfigReq llmReq = new LlmConfigReq();
        BeanUtils.copyProperties(req.getLlmConfig(), llmReq);
        llmConfigBiz.updateLlmConfig(llmReq, AuthContextHolder.getLoginUser().getUid());

        // 更新助手定义
        BotDefinitionEntity botEntity = createBotDefinitionEntity(req, req.getBotCode(), req.getLlmConfig().getLlmCode());
        botDefinitionService.updateByCode(botEntity, botEntity.getBotCode(), BotDefinitionEntity::getBotCode);

        // 同步知识库
        KnowledgeBaseSyncResult syncResult = syncKnowledgeBases(req, req.getLlmConfig().getLlmCode());
        updateBotKnowledgeBasesIfNeeded(botEntity, syncResult);

        // 返回更新结果
        return buildCreateResponse(botEntity.getBotCode(), syncResult);
    }


    /**
     * 校验助手创建|更新请求对象
     *
     * @param req
     * @param isCreate
     */
    private void validateBotDefRequest (BotDefReq req, boolean isCreate) {
        // 模型配置空校验
        Yssert.notNull(req.getLlmConfig(), "模型配置不能为空");

        // 校验知识库数量限制
        LoginUser loginUser = AuthContextHolder.getLoginUser();
        Integer assocKBCount = Optional.ofNullable(sysConfigBiz.getSysConfigByCache("sys.assocKBCount", loginUser.getTenantId()))
                .map(SysConfigVO::getConfigValue).map(Integer::valueOf).orElse(5);
        Yssert.isTrue(CollectionUtils.size(req.getKbmCodes()) <= assocKBCount, "关联的知识库最多" + assocKBCount + "个");

        // 校验场景
        Yssert.isTrue(StringUtils.isBlank(req.getSceneCode()) ||
                        sceneDefService.count(new LambdaQueryWrapper<SceneDefEntity>().eq(SceneDefEntity::getSceneCode, req.getSceneCode())) > 0,
                "关联的场景不存在。");

        // 校验知识库是否存在
        for (String kbmCode : req.getKbmCodes()) {
            KnowledgeBaseVo knowledgeBase = knowledgeBaseManagementBiz.getKnowledgeBaseNameByCode(kbmCode);
            Yssert.notNull(knowledgeBase, "关联的知识库不存在");
        }

        // 校验助手名称
        LambdaQueryWrapper<BotDefinitionEntity> qw = UserHandler.<BotDefinitionEntity>buildTenantLambdaQueryWrapper()
                .eq(BotDefinitionEntity::getBotName, req.getBotName())
                .eq(BotDefinitionEntity::getIsDeleted, 0);

        if (!isCreate) {
            qw.ne(BotDefinitionEntity::getBotCode, req.getBotCode());
        }

        List<BotDefinitionEntity> hasName = botDefinitionService.list(qw);
        Yssert.isTrue(CollectionUtils.isEmpty(hasName), isCreate ? "助手名称已存在,请修改后再次提交" : "助手名称已存在");
    }


    /**
     * 校验头像是否存在，不存在则创建头像
     *
     * @param req
     */
    private void processAvatarPath (BotDefReq req) {
        if (StringUtils.isBlank(req.getAvatarPath())) {
            try {
                // 如果没有上传头像，生成默认头像
                String iconPath = ImageUtils.createIcon(tmpPath, String.valueOf(req.getBotName().charAt(0)));
                logger.info("默认头像生成完毕，保存在{}", tmpPath);

                // 将默认头像上传文件服务
                File file = new File(iconPath);
                MultipartFile files = new MockMultipartFile("files", file.getName(), "image/png", Files.readAllBytes(file.toPath()));
                ActionResponse<FileUploadVo> botAvatar = fileServiceAPI.upload(files, "ai-user-portal", "bot_avatar");
                logger.info("默认头像上传完毕，OssFileCode为{}", botAvatar.getData().getOssFileCode());
                req.setAvatarPath(botAvatar.getData().getOssFileCode());

            } catch (IOException e) {
                logger.info("默认头像生成失败");
                throw new RuntimeException(e);
            }
        }
    }


    /**
     * 构建助手实体对象
     *
     * @param req
     * @param botCode
     * @param llmCode
     * @return
     */
    private BotDefinitionEntity createBotDefinitionEntity (BotDefReq req, String botCode, String llmCode) {
        BotDefinitionEntity entity = new BotDefinitionEntity();
        BeanUtils.copyProperties(req, entity);
        entity.setBotCode(botCode);
        entity.setKbmCode(String.join(",", Safes.of(req.getKbmCodes())));
        String userId = UserHandler.getUserId();
        entity.setCreatedBy(userId);
        entity.setUpdatedBy(userId);
        entity.tranAdvancedConfig(req.getBotAdvancedConfig());
        entity.setLlmCode(llmCode);
        return entity;
    }


    /**
     * 同步知识库到模型
     *
     * @param req
     * @param llmCode
     * @return
     */
    private KnowledgeBaseSyncResult syncKnowledgeBases (BotDefReq req, String llmCode) {
        KnowledgeBaseSyncResult syncResult = new KnowledgeBaseSyncResult();

        if (CollectionUtils.isNotEmpty(req.getKbmCodes())) {
            syncResult = kbsLlmsBiz.knowledgeSyncLLm(llmCode, false);

            // 获取知识库名称映射
            List<KnowledgeBaseVo> knowledgeBases = req.getKbmCodes().stream()
                    .map(knowledgeBaseManagementBiz::getKnowledgeBaseNameByCode)
                    .filter(Objects::nonNull)
                    .toList();

            Map<String, String> kbMaps = knowledgeBases.stream()
                    .collect(Collectors.toMap(KnowledgeBaseVo::getKbmCode, KnowledgeBaseVo::getName, (s, s2) -> s2));

            // 处理同步结果
            List<String> failList = syncResult.getFailList();
            List<String> curBotSuccess = req.getKbmCodes().stream()
                    .filter(s -> !failList.contains(s))
                    .distinct()
                    .toList();

            Yssert.notEmpty(curBotSuccess, "知识库全部关联失败");

            syncResult.setFailList(Safes.of(failList).stream()
                    .map(x -> kbMaps.getOrDefault(x, null))
                    .filter(Objects::nonNull)
                    .toList());

            syncResult.setSuccessList(curBotSuccess);
        }

        return syncResult;
    }


    /**
     * 校验并更新助手知识库关联关系如
     *
     * @param botEntity
     * @param syncResult
     */
    private void updateBotKnowledgeBasesIfNeeded (BotDefinitionEntity botEntity, KnowledgeBaseSyncResult syncResult) {
        if (!syncResult.getSuccessList().isEmpty()) {
            botEntity.setKbmCode(String.join(",", Safes.of(syncResult.getSuccessList())));
            botDefinitionService.updateByCode(botEntity, botEntity.getBotCode(), BotDefinitionEntity::getBotCode);
        }
    }


    /**
     * 构建创建|更新助手响应对象
     *
     * @param botCode
     * @param syncResult
     * @return
     */
    private BotDefCreateVo buildCreateResponse (String botCode, KnowledgeBaseSyncResult syncResult) {
        BotDefCreateVo result = new BotDefCreateVo();
        result.setBotCode(botCode);
        result.setSuccessKbmCodeList(syncResult.getSuccessList());
        result.setFailKbmCodeList(syncResult.getFailList());
        return result;
    }


    /**
     * 助手删除
     *
     * @param botCode
     * @return
     */
    public Boolean deleteDef (String botCode) {
        BotDefinitionEntity bot = botDefinitionService.getByCode(botCode, BotDefinitionEntity::getBotCode);
        Yssert.notNull(bot, "此助手不存在。助手编码：" + bot);
        Yssert.isFalse(bot.getStatus() == 3, "此助手已发布，请操作下线后进行删除操作。");

        // 逻辑删除
        bot.setIsDeleted(-1);

        return botDefinitionService.updateByCode(bot, botCode, BotDefinitionEntity::getBotCode);
//        return botDefinitionService.deleteByCode(botCode, BotDefinitionEntity::getBotCode);
    }


    /**
     * 获取用户推荐问题
     *
     * @return
     */
    public List<UserRecommendQ> getRecommendQ (String botType) {
        LambdaQueryWrapper<BotDefinitionEntity> qw = new LambdaQueryWrapper<BotDefinitionEntity>()
                .eq(StringUtils.isNotBlank(botType), BotDefinitionEntity::getBotType, botType)
                .eq(BotDefinitionEntity::getIsDeleted, 0)
                .orderByDesc(BotDefinitionEntity::getId);
        UserHandler.addTenantQueryColumn(qw);
        // 权限过滤
        if (!AuthContextHolder.getLoginUser().isSuperAdmin() && !AuthContextHolder.getLoginUser().isTenantAdmin()) {
            AuthReq authReq = new AuthReq();
            authReq.setOperateUId(AuthContextHolder.getLoginUser().getUid());
            authReq.setAppCode("ai");
            authReq.setTypes(List.of("assistant"));

            List<String> assistant = dataAuthAPI.authList(authReq).getSuccessData().get("assistant");
            if (CollUtil.isEmpty(assistant)) {
                return List.of();
            }

            qw.in(BotDefinitionEntity::getBotCode, assistant);
        }
        // 助手列表查询
        List<BotDefinitionEntity> bots = botDefinitionService.list(qw);

        List<UserRecommendQ> recommendQs = Safes.of(bots).stream().map(x -> {
            String extendInfo = x.getExtendInfo();
            if (StringUtils.isBlank(extendInfo)) return null;

            JSONArray suggestedQuestions = (JSONArray) JSONUtil.parseObj(extendInfo).get("suggestedQuestions");
            if (CollUtil.isEmpty(suggestedQuestions)) return null;

            String q = Safes.of(suggestedQuestions).stream().filter(a -> Objects.nonNull(a) && StringUtils.isNotBlank(a.toString())).map(Objects::toString).findFirst().orElse("");
            if (StringUtils.isNotBlank(q)) {
                UserRecommendQ userRecommendQ = new UserRecommendQ();
                userRecommendQ.setQuestion(q);
                userRecommendQ.setBotName(x.getBotName());
                userRecommendQ.setBotCode(x.getBotCode());
                userRecommendQ.setBotType(x.getBotType());

                return userRecommendQ;
            }
            return null;
        }).filter(Objects::nonNull).toList();

        return recommendQs.size() > 5 ? recommendQs.subList(0, 5) : recommendQs;
    }


    public List<BotDefVo> latestUseBotList (LastUseBotReq req) {
        if (StringUtils.isEmpty(req.getUserId()))
            req.setUserId(UserHandler.getUserId());
        List<String> botCodeList = redisUtils.getMembersByScoreDesc(req.getUserId() + "-useTime", req.getPageNum(), req.getPageSize());
        List<BotDefinitionEntity> tempList = new ArrayList<>();
        int length = Math.min(req.getPageSize(), botCodeList.size());
        for (int i = 0; i < length; i++) {
            String botCode = botCodeList.get(i);
            LambdaQueryWrapper<BotDefinitionEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BotDefinitionEntity::getBotCode, botCode);
            queryWrapper.eq(BotDefinitionEntity::getIsDeleted, 0);
            BotDefinitionEntity one = botDefinitionService.getOne(queryWrapper);
            tempList.add(one);
        }
        return adaptBotDefVo(tempList);
    }

}
