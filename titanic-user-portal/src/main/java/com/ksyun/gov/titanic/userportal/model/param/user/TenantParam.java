package com.ksyun.gov.titanic.userportal.model.param.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Description:
 * @Author: MYy
 * @Date: 2024/3/12
 **/
@Data
public class TenantParam extends TenantUpdateParam {

    @Schema(title = "账号")
    @NotEmpty(message = "账号不能为空")
    private String account;

    @Schema(title = "密码")
    @NotEmpty(message = "密码不能为空")
    private String password;

    @Schema(title = "电话")
    private String phone;

    @Schema(title = "邮箱")
    private String email;

    @Schema(title = "菜单Ids")
    private List<String> menuIds;
}
