package com.ksyun.gov.titanic.userportal.controller;


import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ksyun.gov.fd.cloud.core.context.auth.AuthContextHolder;
import com.ksyun.gov.fd.cloud.core.web.ActionResponse;
import com.ksyun.gov.fd.common.biz.PageRes;
import com.ksyun.gov.fd.common.lang.Yssert;
import com.ksyun.gov.fd.common.service.api.file.entity.vo.FileInfoVo;
import com.ksyun.gov.fd.common.service.api.file.fegin.FileServiceAPI;
import com.ksyun.gov.fd.common.web.exception.BaseException;
import com.ksyun.gov.fd.common.web.exception.BaseExceptionEnum;
import com.ksyun.gov.fd.common.web.exception.BaseRunTimeException;
import com.ksyun.gov.titanic.userportal.biz.KnowledgeBaseManagementBiz;
import com.ksyun.gov.titanic.userportal.config.aichat.AIClientConfig;
import com.ksyun.gov.titanic.userportal.domain.entity.BotConfigZYPo;
import com.ksyun.gov.titanic.userportal.domain.entity.DifyFileInfoZYPo;
import com.ksyun.gov.titanic.userportal.domain.entity.KnowledgeBaseFileZYPo;
import com.ksyun.gov.titanic.userportal.domain.param.*;
import com.ksyun.gov.titanic.userportal.domain.vo.SharingLinkVo;
import com.ksyun.gov.titanic.userportal.model.param.*;
import com.ksyun.gov.titanic.userportal.model.param.KnowledgeBasePageParam;
import com.ksyun.gov.titanic.userportal.model.vo.*;
import com.ksyun.gov.titanic.userportal.service.BotConfigService;
import com.ksyun.gov.titanic.userportal.service.DifyFileInfoService;
import com.ksyun.gov.titanic.userportal.service.KnowledgeBaseFileZYService;
import com.ksyun.gov.titanic.userportal.service.KnowledgeBaseManagementZYService;
import com.ksyun.gov.titanic.userportal.utils.Sm4RcbUtils;
import com.ksyun.gov.titanic.userportal.utils.TimeUtils;
import com.ksyun.gov.titanic.userportal.utils.UserUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.slf4j.Logger;


/**
 * <p>
 * 知识库管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@RestController
@RequestMapping("/api/knowledge-base")
@Tag(name = "知识库管理")
public class KnowledgeBaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(KnowledgeBaseController.class);
    private final KnowledgeBaseManagementBiz knowledgeBaseManagementBiz;
    @Autowired
    private FileServiceAPI fileServiceAPI;
    @Autowired
    private BotConfigService botConfigService;
    @Autowired
    private DifyFileInfoService difyFileInfoService;
    @Autowired
    private KnowledgeBaseManagementZYService knowledgeService;
    @Autowired
    private KnowledgeBaseFileZYService knowledgeBaseFileService;
    @Autowired
    private AIClientConfig aiClientConfig;


    public KnowledgeBaseController(KnowledgeBaseManagementBiz knowledgeBaseManagementBiz) {
        this.knowledgeBaseManagementBiz = knowledgeBaseManagementBiz;
    }

    @Operation(summary = "根据id删除知识库记录")
    @GetMapping("/delete/{id}")
    public Boolean deleteKnowledgeBase(@PathVariable("id") Long id) {
        knowledgeBaseManagementBiz.removeKnowledgeBase(id);
        return Boolean.TRUE;
    }

    @Operation(summary = "更新知识库记录")
    @PostMapping("/update/{id}")
    public Boolean updateKnowledgeBase(@PathVariable("id") Long id, @Validated @RequestBody KnowledgeBaseParam knowledgeBaseParam) {
        knowledgeBaseManagementBiz.updateKnowledgeBase(id, knowledgeBaseParam,AuthContextHolder.getLoginUser().getUid());
        return Boolean.TRUE;
    }

    @Operation(summary = "新增知识库记录")
    @PostMapping("/add")
    public Boolean saveKnowledgeBase(@Validated @RequestBody KnowledgeBaseParam knowledgeBaseParam) {
        knowledgeBaseManagementBiz.saveKnowledgeBase(knowledgeBaseParam, AuthContextHolder.getLoginUser().getUid());
        return Boolean.TRUE;
    }

    @Operation(summary = "根据id查询知识库详情接口")
    @GetMapping("/{id}")
    public KnowledgeBaseVo getKnowledgeBase(@PathVariable("id") Long id) {
        return knowledgeBaseManagementBiz.getKnowledgeBaseV2(id);
    }

    @Operation(summary = "根据code查询知识库名称")
    @GetMapping("/name/{code}")
    public KnowledgeBaseVo getKnowledgeBaseNameByCode(@PathVariable("code") String code) {
        return knowledgeBaseManagementBiz.getKnowledgeBaseNameByCodeV2(code);
    }


    /**
     * @param knowledgeBasePageParam 传参
     * @return 分页列表
     * @description 获取知识库分页列表
     */

    @Operation(summary = "查询知识库列表")
    @PostMapping("/page")
    public PageRes<KnowledgeBaseVo> listKnowledgeBaseVos(@Validated @RequestBody KnowledgeBasePageParam knowledgeBasePageParam) {
        return knowledgeBaseManagementBiz.listKnowledgeBaseVos(knowledgeBasePageParam);
    }

    @Operation(summary = "查询知识库列表-获取全部为助手下拉提供")
    @PostMapping("/allKnowledge")
    public List<Map<String,String>> listAllKnowledge(@Validated @RequestBody KnowledgeBaseAllParam  knowledgeBaseAllParam) {
        return knowledgeBaseManagementBiz.listAllKnowledge(knowledgeBaseAllParam);
    }

    /**
     * 查询个人知识库编码
     *
     * @return
     */
    @Operation(summary = "查询个人知识库编码")
    @GetMapping("/pkbCode")
    public String getPkbCode() {
        return knowledgeBaseManagementBiz.getPkbCode(AuthContextHolder.getLoginUser().getUid());
    }

    // todo:以下文件操作废弃

    @Operation(summary = "保存知识库文件信息")
    @PostMapping("/save/file-info/{kbmCode}")
    public List<String> saveKnowledgeBaseFileInfo (@PathVariable("kbmCode") String kbmCode, @RequestBody List<KnowledgeBaseFileParam> params) {
        Yssert.notEmpty(params, "上传文件信息为空");
        return knowledgeBaseManagementBiz.saveKnowledgeBaseFileInfo(kbmCode, params);
    }


    @Operation(summary = "保存知识库文件信息V2")
    @PostMapping("/save/file-info/v2/{kbmCode}")
    public List<KBFileUploadFailVo> saveKnowledgeBaseFileInfoV2 (@PathVariable("kbmCode") String kbmCode, @RequestBody List<KnowledgeBaseFileParam> params) {
        Yssert.notEmpty(params, "上传文件信息为空");
        return knowledgeBaseManagementBiz.saveKnowledgeBaseFileInfoV2(kbmCode, params);
    }


    @Operation(summary = "保存知识库文件信息")
    @PostMapping("/save/one-file-info/{kbmCode}")
    public String saveOneKnowledgeBaseFileInfo (@PathVariable("kbmCode") String kbmCode, @Validated @RequestBody KnowledgeBaseFileParam param) {
        Yssert.notNull(param, "上传文件信息为空");
        return knowledgeBaseManagementBiz.saveOneKnowledgeBaseFileInfo(kbmCode, param);
    }

    @Operation(summary = "文件列表")
    @GetMapping("/file/list")
    public List<KnowledgeBaseFileVo> listKnowledgeBaseFileVos (@RequestParam("kbmCode") String kbmCode,
                                                               @RequestParam(value = "synStatus", required = false) String synStatus,
                                                               @RequestParam(value = "fileName", required = false) String fileName) {
        return knowledgeBaseManagementBiz.getKnowledgeBaseFileList(kbmCode, fileName, synStatus);
    }


    @Operation(summary = "文件列表-分页")
    @GetMapping("/file/pageList")
    public PageRes<KnowledgeBaseFileVo> pageKnowledgeBaseFileVos (KnowledgeBaseFileQueryParam queryParam) {
        return knowledgeBaseManagementBiz.pageKnowledgeBaseFileList(queryParam);
    }

    @Operation(summary = "删除知识库文件")
    @PostMapping("/delete/knowledge-file")
    public Boolean deleteKnowledgeFileByCode (@RequestBody List<String> fileCodes) throws BaseRunTimeException {
        for (String fileCode : fileCodes) {
            try {
                knowledgeBaseManagementBiz.deleteKnowledgeFileByCode(fileCode);
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
                throw new BaseRunTimeException(BaseExceptionEnum.SYS_ERROR.getCode(), e.getMessage());
            }
        }

        return Boolean.TRUE;
    }

    @Operation(summary = "根据fileCodes获取文件信息列表")
    @PostMapping("/knowledge-file-info")
    public List<KnowledgeBaseFileVo> getKnowledgeFileInfoByFileCode (@Validated @RequestBody List<String> fileCodes) {
        Yssert.notEmpty(fileCodes, "上传文件信息为空");
        return knowledgeBaseManagementBiz.getKnowledgeFileInfoByFileCode(fileCodes, AuthContextHolder.getLoginUser().getTenantId());
    }

    @Operation(summary = "获取文件同步信息")
    @GetMapping("/file/syn-info")
    public List<FileSynInfoVo> listFileSynInfo (@NotBlank @RequestParam("fileCode") String fileCode, @RequestParam(value = "llmCode", required = false) String llmCode) {
        return knowledgeBaseManagementBiz.listFileSynInfo(fileCode, llmCode);
    }


    @PostMapping("/retry/file-syn")
    public Boolean retryFileSyn (@Validated @RequestBody RetryFileSynParam param) {
        knowledgeBaseManagementBiz.retryFileSyn(param);
        return Boolean.TRUE;
    }


    @Operation(summary = "首页/创建临时知识库")
    @PostMapping("/create/tempKbm")
    public String createTempKbm(@RequestParam(required = false) String llmCode) {
        if (StringUtils.isBlank(llmCode)) {
            llmCode = aiClientConfig.getDefaultLLmConfigVo().getLlmCode();
        }

        return knowledgeBaseManagementBiz.createTempKbm(llmCode);
    }


    // 对话附件
    @Operation(summary = "首页/创建临时会话知识库")
    @PostMapping("/create/tempChatKbm")
    public String createTempChatKbm(@RequestBody CreateTempChatKbmParam createTempChatKbmParam) {
        String chatCode = createTempChatKbmParam.getChatCode();
        String llmCode = createTempChatKbmParam.getLlmCode();
        if (StringUtils.isBlank(llmCode)) {
            llmCode = aiClientConfig.getDefaultLLmConfigVo().getLlmCode();
        }
        return knowledgeBaseManagementBiz.createTempChatKbm(llmCode, chatCode);
    }


    @Operation(summary = "首页/上传对话知识库文件")
    @PostMapping("/save/chatKb/{kbmCode}")
    public String saveChatKnowledgeBaseFileInfo(@PathVariable("kbmCode") String kbmCode, @Validated @RequestBody KnowledgeBaseFileParam param) {
        Yssert.notNull(param, "上传文件信息为空");
        return knowledgeBaseManagementBiz.saveChatKnowledgeBaseFileInfo(kbmCode, param);
    }


    @Operation(summary = "附件上传")
    @PostMapping("/uploadAtt")
    public UploadAttVo uploadAtt(@Validated @RequestBody UploadAttParam uploadAttParam) {
        return knowledgeBaseManagementBiz.uploadAtt(uploadAttParam.getChatCode(), uploadAttParam.getChatKbCode(), uploadAttParam.getKbFile());
    }

    @Operation(summary = "获取文件摘要信息")
    @GetMapping("/file/abstract-info/{fileCode}")
    public Object getFileAbstractInfo(@PathVariable("fileCode") String fileCode) {
        return knowledgeBaseManagementBiz.getFileAbstractInfoByFileCode(fileCode);
    }




    // todo:以下为知跃操作文件的方法移动至AI门户
    @Operation(summary = "文件上传")
    @PostMapping("/file/upload")
    public String uploadFile(@Validated @RequestBody List<KnowledgeBaseFileZYParam> params) {
        Yssert.notEmpty(params, "上传文件信息为空");
        return knowledgeService.uploadFile(params);
    }
    @Operation(summary = "文件/文件夹展示(树状)")
    @PostMapping("/file/page")
    public PageRes<com.ksyun.gov.titanic.userportal.domain.vo.KnowledgeBaseFileVo> listKnowledgeBaseFile(@Validated @RequestBody KnowledgeBaseFilePageParam param) {
        return knowledgeService.listKnowledgeBaseFile(param);
    }
    @Operation(summary = "code查询文件夹信息-分页")
    @PostMapping("/file/bycode-page")
    public  PageRes<com.ksyun.gov.titanic.userportal.domain.vo.KnowledgeBaseFileVo> listKnowledgeBaseFileByCode(@Validated @RequestBody KnowledgeBaseFileSearchPageParam param) {
        return knowledgeBaseFileService.listKnowledgeBaseFileByCode(param);
    }

    @Operation(summary = "codo查询文件夹信息")
    @PostMapping("/file/bycode")
    public  List<com.ksyun.gov.titanic.userportal.domain.vo.KnowledgeBaseFileVo> listKnowledgeBaseFileByCode(@Validated @RequestBody KnowledgeBaseFileSearchParam param) {
        return knowledgeService.listKnowledgeBaseFileByCode(param);
    }

    @Operation(summary = "文件修改")
    @PostMapping("/file/update")
    public com.ksyun.gov.titanic.userportal.domain.vo.KnowledgeBaseFileVo updateKnowledgeBaseFile(@Validated @RequestBody KnowledgeBaseFileUpdateParam param) {
        return knowledgeService.updateKnowledgeBaseFile(param);
    }
    @Operation(summary = "删除文件")
    @PostMapping("/file/delete-batch")
    public String deleteFile(@RequestBody List<String> codes) {
        Assert.notEmpty(codes, "知识库文件code为空!");
        knowledgeService.deleteFile(codes);
        return "成功";
    }

    @PostMapping("/copy")
    public Boolean knowledgeCopy(@Validated @RequestBody KnowledgeCopyParam param) {
        return knowledgeService.knowledgerBaseFileCopy(param);
    }

    @PostMapping("/move")
    public Boolean knowledgeMove(@Validated @RequestBody KnowledgeCopyParam param) {
        return knowledgeService.knowledgerBaseFileMove(param);
    }

    @GetMapping("/sharing-link")
    public SharingLinkVo getSharingLink(@RequestParam("kbmCode") String kbmCode, @RequestParam("minuteNum") Integer minuteNum, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String userId = UserUtils.getUserId();
        Assert.notBlank(userId, "用户信息为空!");
        SharingLinkParam sharingLinkParam = new SharingLinkParam();
        sharingLinkParam.setKbmCode(kbmCode);
        sharingLinkParam.setUserId(userId);
        sharingLinkParam.setDeadline(TimeUtils.TimeCalculatorByMinute(minuteNum));
        String encode = Sm4RcbUtils.encode(JSONUtil.toJsonStr(sharingLinkParam), null);
        SharingLinkVo sharingLinkVo = new SharingLinkVo();
        sharingLinkVo.setUrl((requestURI + "/" + encode).replaceAll("^.*/llm-knowledge", "/llm-knowledge"));
        return sharingLinkVo;
    }

    @GetMapping("/sharing-link/{content}")
    public Boolean sharingLink(@PathVariable("content") String content) {
        try {
            String decode = Sm4RcbUtils.decode(content, null);
            SharingLinkParam bean = JSONUtil.toBean(decode, SharingLinkParam.class);
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(UserUtils.getUserId(), bean.getUserId())) {
                return Boolean.TRUE;
            }
            knowledgeService.sharingLink(bean);
        } catch (InvalidKeyException | UnsupportedEncodingException | IllegalBlockSizeException | BadPaddingException |
                 NoSuchAlgorithmException | NoSuchProviderException | NoSuchPaddingException e) {
            throw new RuntimeException(e);
        }
        return Boolean.TRUE;
    }

    @GetMapping("/privewurl")
    public FileInfoVo privewurl(@RequestParam("fileCode") String fileCode, @RequestParam("type") int type) {
        // 会话
        if (type == 1) {
            // 查询dify中的文件是否存在
            DifyFileInfoZYPo one = difyFileInfoService.getOne(new LambdaQueryWrapper<DifyFileInfoZYPo>().eq(DifyFileInfoZYPo::getDocumentId, fileCode));
            if (Objects.isNull(one)) {
                throw new BaseException("500", "文件不存在");
            } else {
                KnowledgeBaseFileZYPo one1 = knowledgeBaseFileService.getOne(new LambdaQueryWrapper<KnowledgeBaseFileZYPo>().eq(KnowledgeBaseFileZYPo::getCode, one.getKnowledgeFileCode()));
                if (Objects.isNull(one1)) {
                    throw new BaseException("500", "文件不存在");
                }
                fileCode = one1.getOssFileCode();
            }
        }
        ActionResponse<FileInfoVo> fileItems = fileServiceAPI.getFileItem(fileCode);
        return fileItems.getSuccessData();
    }

    @GetMapping("/queryBot")
    public Object queryBot() {
        LambdaQueryWrapper<BotConfigZYPo> lambdaQueryWrapper = new LambdaQueryWrapper<>(BotConfigZYPo.class);
        lambdaQueryWrapper.eq(BotConfigZYPo::getIsDeleted, 0);
        lambdaQueryWrapper.eq(BotConfigZYPo::getCreatedBy, UserUtils.getUserId());
        lambdaQueryWrapper.eq(BotConfigZYPo::getTenantId, UserUtils.getUserInfo().getTenantId());
        lambdaQueryWrapper.orderByDesc(BotConfigZYPo::getCreatedTime);
        List<BotConfigZYPo> list = botConfigService.list(lambdaQueryWrapper);


        Map<String, List<Map<String, String>>> groupedData = list.stream()
                .collect(Collectors.groupingBy(
                        BotConfigZYPo::getBotCode, // 分组依据：botCode
                        Collectors.mapping(
                                item -> {
                                    Map<String, String> map = new HashMap<>();
                                    map.put("code", item.getCode());
                                    map.put("name", item.getName());
                                    return map;
                                },
                                Collectors.toList() // 收集为列表
                        )
                ));

        Map<String, Object> resultMap = new HashMap<>();
        groupedData.entrySet().forEach(entry -> {
            resultMap.put("botCode", entry.getKey());
            resultMap.put("params", entry.getValue());
        });
        return resultMap;
    }


    @PostMapping("/share-content")
    public Boolean shareContentToKnowledge(@RequestBody ContentShareParam param) {
        knowledgeService.shareContentToKnowledge(param);
        return Boolean.TRUE;
    }

}

