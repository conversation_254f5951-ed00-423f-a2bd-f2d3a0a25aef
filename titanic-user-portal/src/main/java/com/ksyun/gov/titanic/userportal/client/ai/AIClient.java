package com.ksyun.gov.titanic.userportal.client.ai;

import com.ksyun.gov.titanic.userportal.client.ai.dify.model.*;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.client.AddLLMKnowledgeParam;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.client.UpdateMetadataListInfoParam;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.titanic.ChatReq;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.titanic.ChatRes;
import com.ksyun.gov.titanic.userportal.client.ai.dify.model.titanic.WorkRunReq;
import okhttp3.sse.EventSourceListener;

import java.io.File;
import java.util.Set;

/***
 *@title AIClient
 *@description <TODO description class purpose>
 *<AUTHOR>
 *@version 1.0.0
 *@create 2025/3/24 11:24
 **/
public interface AIClient {
    /**
     *
     * @param request
     * @return
     */
    @Deprecated
    ChatRes chat(ChatReq request);

    String getKeyMetadataAuthorityName();

    /**
     * 聊天
     * @param request
     * @param resultListener
     */
    void chatStream(ChatReq request, EventSourceListener resultListener,String traceId);

    void workflowsRun(WorkRunReq request, EventSourceListener resultListener,String traceId);

    /**
     * 获取知识库对应的datasetId
     *
     * @param addLLMKnowledgeParam 项目使用标签用户id 进行分发到每个知识库中
     * @return datasetId 这个需要保存
     */
    DatasetsInfoResp.DataSetInfo findKnowledgeIdByTagKey(AddLLMKnowledgeParam addLLMKnowledgeParam);

    /**
     * 查询对应知识库库下元数据属性(不包括元数据值,包括的id,type,name)
     * @param datasetId
     * @return
     */
    FindMetadataResp queryMetadataInfo(String datasetId);


    /**
     *      * 更新和新增元数据的值在文档中进行添加
     * @param updateMetadataListInfoParam
     * @param traceId
     * @return
     */
    boolean updateAllMetadataInfo(UpdateMetadataListInfoParam updateMetadataListInfoParam, String traceId);

    /**
     * 新增文件信息
     * @param datasetId
     * @param file
     * @return
     */
    Document addKbFile(String datasetId, File file);

    Document addKbFileV2(String datasetId, File file);

    DatasetFileCreateResp addKbFileReturnInfo(String datasetId, File file);

    void delKbFile(String datasetId, String documentId,String traceId);

    /**
     * 批量删除文档
     * @param datasetId
     * @param documentIds 不要超过数量20个
     * @param traceId
     */
    void batchDeleteDocument(String datasetId, Set<String> documentIds, String traceId);

    AddLLMKnowledgeMode addLLMKnowledge(AddLLMKnowledgeParam addLLMKnowledgeParam, String traceId, File file,String setId);
}
